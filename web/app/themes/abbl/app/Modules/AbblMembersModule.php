<?php

namespace App\Modules;

class AbblMembersModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        register_extended_post_type(CPT_ABBL_MEMBER, [
            'menu_position' => 12,
            'has_archive' => true,
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-groups',
            'supports' => ['title'],
            'taxonomies' => [],
        ], [
            'singular' => 'ABBL member',
            'plural' => 'ABBL members',
            'slug' => 'abbl-members',
        ]);

        register_extended_taxonomy(TAX_ABBL_MEMBER_CATEGORY, CPT_ABBL_MEMBER, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Member categories',
            'singular' => 'Member category',
        ]);
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = CPT_ABBL_MEMBER;
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;

        return new \WP_Query($query);
    }
}