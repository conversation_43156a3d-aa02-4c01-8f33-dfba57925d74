<?php

namespace App\Options;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Options as Field;

class SiteOptions extends Field
{
    /**
     * The option page menu name.
     *
     * @var string
     */
    public $name = 'Site Options';

    /**
     * The option page document title.
     *
     * @var string
     */
    public $title = 'Site Options | Options';

    /**
     * The option page field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('site_options');

        $fields
            ->addTab('pages')
                ->addPostObject('page_professionals_home', [
                    'label' => __('Professionals home', 'abbl-backend'),
                    'post_type' => 'page',
                ])
                ->addPostObject('page_consumers_home', [
                    'label' => __('Consumers home', 'abbl-backend'),
                    'post_type' => 'page',
                ])
                ->addPostObject('page_privacy_policy', [
                    'label' => __('Privacy policy', 'abbl-backend'),
                    'post_type' => 'page',
                ])
            ->addTab('footer')
                ->addTextarea('footer_logo_tagline', [
                    'label' => __('Logo tagline', 'abbl-backend'),
                ])
            ->addTab('alert_banner')
                ->addText('alert_banner_icon', [
                    'label' => __('Icon', 'abbl-backend'),
                ])
                ->addWysiwyg('alert_banner_message', [
                    'label' => __('Message', 'abbl-backend'),
                ])
            ->addTab('events')
                ->addWysiwyg('events_default_disclaimer', [
                    'label' => __('Default disclaimer', 'abbl-backend'),
                ])
            ->addTab('newsletter')
                ->addText('newsletter_default_title', [
                    'label' => __('Default title', 'abbl-backend'),
                ])
                ->addWysiwyg('newsletter_default_content', [
                    'label' => __('Default content', 'abbl-backend'),
                ]);

        return $fields->build();
    }
}
