<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class BigNumber extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('big_number');

        $fields
            ->addText('big_number_number', [
                'label' => __('Number', 'abbl-backend'),
            ])
            ->addWysiwyg('big_number_content', [
                'label' => __('Content', 'abbl-backend'),
            ]);

        return $fields;
    }
}
