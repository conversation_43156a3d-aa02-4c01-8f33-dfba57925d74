<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class TextAndBigNumber extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('text_and_big_number');

        $fields
            ->addWysiwyg('text')
            ->setWidth(50)
            ->addGroup('figure')
                ->setWidth(50)
                ->addText('number')
                ->addText('unit')
                ->addWysiwyg('content')
            ->endGroup();

        return $fields;
    }
}
