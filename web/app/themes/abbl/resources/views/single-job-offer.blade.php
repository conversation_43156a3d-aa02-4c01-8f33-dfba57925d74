@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
            the_post();

            $contract_type = get_the_terms(get_the_ID(), TAX_JOB_OFFER_CONTRACT_TYPE)[0] ?? null;
            $is_abbl_job = abbl_user_is_in_abbl_team(get_the_author_meta('ID'));

            $author_id = get_field('is_abbl_job');

            $website_url = get_field('company_website_url', 'user_'.$author_id);
            $linkedin_url = get_field('company_linkedin_url', 'user_'.$author_id);
            $company_logo = get_field('company_logo', 'user_'.$author_id);
            $company_name = get_field('company_name', 'user_'.$author_id);
            $company_location = get_field('company_location', 'user_'.$author_id);

            $publication_date = Carbon\Carbon::createFromTimestamp(get_the_date('U'));
            $deadline = get_field('closes_at') ? Carbon\Carbon::createFromFormat('d/m/Y', get_field('closes_at')) : null;
        @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>{{ $company_name }}</p>
            @if($company_location)
                <p>{{ $company_location }}</p>
            @endif
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <div class="my-8">
                    <a href="#"><i class="fas fa-arrow-left"></i> {{ __('Back to Job offers', 'abbl') }}</a>
                </div>
            </x-container>
        </x-section>

        <x-section>
            <x-container>
                <div class="grid grid-cols-[2fr_1fr] gap-8">
                    <div>
                        <div class="space-y-4 p-4 mb-8 bg-gray-200">
                            <div class="flex items-center gap-8 mb-4">
                                @if($company_logo)
                                    <img class="max-w-[220px] max-h-[40px]" src="{{ $company_logo['url'] }}"
                                         alt="{{ $company_logo['alt'] }}">
                                @endif
                                <div class="space-y-1">
                                    <h2 class="text-2xl font-bold">{{ __('About the company', 'abbl') }}</h2>
                                    @if($website_url)
                                        <p>
                                            <i class="fas fa-globe mr-1"></i>
                                            <a href="{{ $website_url }}" target="_blank">{{ $website_url }}</a>
                                        </p>
                                    @endif
                                    @if($company_location)
                                        <p>
                                            <i class="fas fa-location-dot mr-1"></i>
                                            <a href="{{ $company_location }}">{{ $company_location }}</a>
                                        </p>
                                    @endif
                                </div>
                            </div>
                            <div class="wysiwyg">
                                <h2>{{ __('About the company', 'abbl') }}</h2>
                                {!! get_field('company_description', 'user_'.$author_id) !!}
                            </div>

                            @if($website_url || $linkedin_url)
                                <div class="flex gap-4">
                                    @if($website_url)
                                        <x-link-button href="{{ $website_url }}" target="_blank">
                                            {{ __('Visit website', 'abbl') }}
                                        </x-link-button>
                                    @endif
                                    @if($linkedin_url)
                                        <x-link-button href="{{ $linkedin_url }}" target="_blank" variant="outline">
                                            <i class="fab fa-linkedin mr-3 text-lg"></i>
                                            {{ __('View LinkedIn page', 'abbl') }}
                                        </x-link-button>
                                    @endif
                                </div>
                            @endif
                        </div>

                        <div class="wysiwyg">
                            {!! get_the_content() !!}
                        </div>
                    </div>
                    <div class="space-y-4">
                        @if($is_abbl_job)
                            <div class="p-4 bg-gray-200">
                                <p class="font-bold">{{ __('This position is open within the ABBL team', 'abbl') }}</p>
                            </div>
                        @endif
                        <div class="p-4 bg-gray-200">
                            @if($contract_type)
                                <p>
                                    <strong>Contract:</strong>
                                    {{ $contract_type->name }}
                                </p>
                            @endif
                            @if($deadline)
                                <p>
                                    <strong>Application deadline:</strong>
                                    {{ $deadline->format(ABBL_DATE_FORMAT_HUMAN) }}
                                </p>
                            @endif
                            @if($publication_date)
                                <p>
                                    <strong>Published on:</strong>
                                    {{ $publication_date->format(ABBL_DATE_FORMAT_HUMAN) }}
                                </p>
                            @endif
                        </div>
                        <div>
                            <h2 class="mb-4 text-2xl font-bold">{{ __('Apply', 'abbl') }}</h2>
                            <div class="p-4 bg-gray-300">
                                <div class="wysiwyg mb-4">
                                    {!! get_field('apply_content') !!}
                                </div>
                                @if($apply_button_url = get_field('apply_button_url'))
                                    <a class="inline-block px-4 py-2 bg-gray-400" href="{{ $apply_button_url['url'] }}"
                                       target="_blank">
                                        {{ $apply_button_url['title'] }}
                                    </a>
                                @endif
                            </div>
                        </div>
                        <div class="p-4 bg-gray-200">
                            <a href="#" class="flex items-center gap-8">
                                <i class="fas fa-share-nodes text-2xl"></i>
                                <p class="font-bold">{{ __('Share this job offer on LinkedIn') }}</p>
                            </a>
                        </div>
                        <div class="space-y-4 p-4 bg-gray-200">
                            <h2 class="text-xl font-bold">Careers in Luxembourg</h2>
                            <p>Nunc ultricies eros sit amet venenatis gravida. Nullam ac tempor libero, in iaculis
                                justo. Nunc erat nisi, volutpat scelerisque scelerisque eget, malesuada ut eros.</p>
                            <x-link-button href="#" variant="outline">{{ __('Read more', 'abbl') }}</x-link-button>
                        </div>

                        <div class="space-y-4 p-4 border border-gray-200">
                            <h2 class="text-xl font-bold">{{ __('Member of ABBL ? ', '') }}</h2>
                            <p>As a member of ABBL, you can publish your job offers on the ABBL website.</p>
                            <x-link-button href="#">{{ __('How to publish your job offer', 'abbl') }}</x-link-button>
                        </div>
                    </div>
                </div>
            </x-container>
        </x-section>

        <x-section>
            <x-flexibles.newsletter
                title="Never miss a new job opportunity <br> Stay informed about the latest job openings in Luxembourg’s financial sector."
                content="<p>Subscribe to our newsletter</p>"
                :show-linkedin="true"
            />
        </x-section>

    @endwhile
@endsection
