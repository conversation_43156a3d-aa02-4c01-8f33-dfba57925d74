{{--
  Template Name: Global homepage
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <section class="relative pt-6 pb-[5.625rem] md:pt-18 md:pb-34 bg-abbl-gradient-grain">
            <x-container>
                <div class="pt-4 pb-6 px-8 md:px-25 md:pt-13 md:pb-10 rounded-lg bg-navy-blue/50">
                    <div class="mb-5 md::mb-15 text-center">
                        <h1 class="md:mb-9 font-headings leading-[1.25] text-[1.25rem] lg:text-[1.75rem] font-bold text-neon-green">
                            Welcome to ABBL <br>
                            The Luxembourg’s Bankers’ Association
                        </h1>
                        <p class="hidden lg:block font-semibold text-white">
                            The ABBL supports both finance professionals and the general public. <br>
                            Please choose your path below.
                        </p>
                    </div>
                    <div class="flex justify-around md:hidden">
                        <div class="flex gap-4 p-2 mb-7 rounded-full bg-white/75">
                            <button aria-label="{{ __('View professionals panel', 'abbl') }}" class="py-3 px-4 rounded-full bg-navy-blue font-bold text-white">Professional</button>
                            <button aria-label="{{ __('View consumers panel', 'abbl') }}" class="py-3 px-4 rounded-full">Consumer</button>
                        </div>
                    </div>
                    <div class="overflow-hidden flex lg:grid grid-cols-2 gap-5 mb-10 rounded-lg md:rounded-none">
                        <section class="overflow-hidden shrink-0 w-full flex flex-col rounded-none md:rounded-l-lg">
                            <div class="flex flex-col justify-center items-center pt-4 pb-6 md:pt-12 md:pb-10 px-6 text-center text-white bg-navy-blue">
                                <img class="md:order-3 block w-full h-[4.5rem] md:h-[13.75rem] mb-2 md:mb-0 object-fit object-center" src="{{ Vite::asset('resources/images/needs-integration/home-bank.svg') }}" alt="">
                                <h2 class="md:mb-1 text-[1.75rem] md:text-[1.25rem] font-bold">I'm a professional</h2>
                                <p class="md:mb-8">Content and services tailored <br> to finance professionals</p>
                            </div>
                            <div class="grow flex flex-col justify-center items-center gap-8 text-center pt-8 pb-11 px-8 bg-white">
                                <x-link-button class="md:order-2 md:mt-auto" id="home-professionals-button" href="{{ get_permalink(ID_HOMEPAGE_PRO) }}">
                                    <span>ABBL For professionals</span>
                                    <i class="far fa-arrow-right text-[1.5rem]"></i>
                                </x-link-button>
                                <p class="text-[0.875rem]">The ABBL focuses on the topics which matter most to financial professionals, and which will have the greatest impact on their business in the years to come.</p>
                            </div>
                        </section>
                        <section class="overflow-hidden shrink-0  w-fullflex flex-col rounded-none md:rounded-r-lg">
                            <div class="flex flex-col justify-center items-center pt-4 pb-6 md:pt-12 md:pb-10 px-6 text-center text-navy-blue bg-neon-green">
                                <img class="md:order-3 block w-full h-[4.5rem] md:h-[13.75rem] mb-2 md:mb-0 object-fit object-center" src="{{ Vite::asset('resources/images/needs-integration/home-piggybank.svg') }}" alt="">
                                <h2 class="md:mb-1 text-[1.75rem] md:text-[1.25rem] font-bold">I'm a consumer</h2>
                                <p class="md:mb-8">Useful information for the general <br> public and banking clients</p>
                            </div>
                            <div class="grow flex flex-col justify-center items-center gap-8 text-center pt-8 pb-11 px-8 bg-white">
                                <x-link-button class="md:order-2 md:mt-auto" id="home-consumers-button" href="{{ get_permalink(ID_HOMEPAGE_CONSUMERS) }}">
                                    <span>ABBL For consumers</span>
                                    <i class="far fa-arrow-right text-[1.5rem]"></i>
                                </x-link-button>
                                <p class="text-[0.875rem]">The ABBL provides practical information and resources to help consumers better understand banking services, manage their finances, and make informed decisions.</p>
                            </div>
                        </section>
                    </div>
                    <div class="flex justify-center items-center gap-6 text-white">
                        <i class="far fa-arrow-right-arrow-left text-[1.5rem] text-neon-green"></i>
                        <p class="font-medium">You can switch sections at any time via the top-left menu.</p>
                    </div>
                </div>

                {{--
                <div class="grid gap-4 p-12 bg-white">
                    <div>
                        <x-link-button>Normal - Primary</x-link-button>
                    </div>
                    <div>
                        <x-link-button class="bg-neon-green">Normal - Secondary</x-link-button>
                    </div>
                    <div>
                        <x-link-button size="sm">Small - Primary</x-link-button>
                    </div>
                    <div>
                        <x-link-button size="sm" class="bg-neon-green">Small - Secondary</x-link-button>
                    </div>

                    <div>
                        <x-link-button variant="outline">Normal - Primary - Outlined</x-link-button>
                    </div>
                    <div>
                        <x-link-button variant="outline" class="border-neon-green">Normal - Secondary - Outlined</x-link-button>
                    </div>
                    <div>
                        <x-link-button size="sm" variant="outline">Small - Primary - Outlined</x-link-button>
                    </div>
                    <div>
                        <x-link-button size="sm" variant="outline" class="border-neon-green">Small - Secondary - Outlined</x-link-button>
                    </div>
                </div>
                --}}
            </x-container>
        </section>
    @endwhile
@endsection

@push('head-first-scripts')
    <script>
        const userType = window.localStorage.getItem('abblUserType');

        if (userType === 'professional') {
            window.location = '{{ get_permalink(ID_HOMEPAGE_PRO) }}';
        }

        if (userType === 'consumer') {
            window.location = '{{ get_permalink(ID_HOMEPAGE_CONSUMERS) }}';
        }
    </script>
@endpush

@push('footer-scripts')
    <script>
        const professionalsButton = document.querySelector('#home-professionals-button');
        const consumersButton = document.querySelector('#home-consumers-button');

        professionalsButton.addEventListener('click', (e) => {
            e.preventDefault()

            window.localStorage.setItem('abblUserType', 'professional');

            window.location = e.target.href;
        });

        consumersButton.addEventListener('click', (e) => {
            e.preventDefault()

            window.localStorage.setItem('abblUserType', 'consumer');

            window.location = e.target.href;
        });
    </script>
@endpush
