<?php

namespace App\Modules;

class HotTopicsModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        register_extended_post_type(CPT_HOT_TOPIC, [
            'menu_position' => 4,
            'has_archive' => true,
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-buddicons-friends',
            'supports' => ['title', 'editor', 'thumbnail', 'excerpt'],
            'taxonomies' => [],
        ], [
            'slug' => 'hot-topics',
        ]);

        register_extended_taxonomy(TAX_HOT_TOPIC_CATEGORY, CPT_HOT_TOPIC, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Hot topic categories',
            'singular' => 'Hot topic category',
        ]);
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = CPT_HOT_TOPIC;
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;

        return new \WP_Query($query);
    }
}