<?php

namespace App\Fields\PostTypes;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Person extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('person');

        $fields
            ->setLocation('post_type', '==', CPT_PERSON);

        $fields
            ->addText('first_name', [
                'label' => __('First name', 'abbl-backend'),
            ])
                ->setWidth(50)
            ->addText('last_name', [
                'label' => __('Last name', 'abbl-backend'),
            ])
                ->setWidth(50)
            ->addText('job_title', [
                'label' => __('Job title', 'abbl-backend'),
            ])
            ->addEmail('email', [
                'label' => __('Email', 'abbl-backend'),
            ])
            ->addUrl('linkedin_url', [
                'label' => __('Linkedin URL', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
