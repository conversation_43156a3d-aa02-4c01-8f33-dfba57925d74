@php
$category = get_the_terms(get_the_ID(), TAX_EVENT_CATEGORY)[0] ?? null;
$venue = get_the_terms(get_the_ID(), TAX_EVENT_VENUE)[0] ?? null;
$languages = get_the_terms(get_the_ID(), TAX_EVENT_LANGUAGE) ?: [];
$partners = range(0, rand(0, 3)); // TODO: Change this
$featured = get_field('is_featured');

$start_date = abbl_acf_to_carbon(get_field('start_datetime'));
$end_date = abbl_acf_to_carbon(get_field('end_datetime'));
@endphp

<article {{ $attributes->twMerge('flex flex-col p-5 rounded-lg bg-light-blue' . ($featured ? ' glow-neon-green border-neon-green' : ' relative z-10')) }}>
    <div class="flex justify-between items-center pb-4">
        <div class="flex items-center gap-3">
            @if($category)
                <x-category-badge variant="outline" :category="$category"/>
            @endif
            @if($featured)
                <x-badge class="text-neon-green text-[0.75rem] font-bold bg-navy-blue">{{ __('Featured', 'abbl') }}</x-badge>
            @endif
        </div>
        <div class="flex items-center gap-4">
            @if($languages && count($languages))
                <div class="flex items-center gap-2">
                    @foreach ($languages as $language)
                        <x-language-badge :language="$language" />
                    @endforeach
                </div>
            @endif
        </div>
    </div>
    @if ($start_date)
        <div class="mb-2">
            <p class="text-[0.875rem] font-semibold">{{ $start_date->format(ABBL_DATE_FORMAT_HUMAN) }}</p>
            <p class="text-[0.8125rem] font-medium">
                {{ $start_date->format(ABBL_TIME_FORMAT) }}
                @if ($end_date)
                    - {{ $end_date->format(ABBL_TIME_FORMAT) }}
                @endif
            </p>
        </div>
    @endif
    <h3 class="mb-4 font-headings text-[1.125rem] font-bold">{!! get_the_title() !!}</h3>
    @if($excerpt = get_the_excerpt())
        <div class="mb-5 text-[0.8125rem]">
            <p>{!! $excerpt !!}</p>
        </div>
    @endif
    <div class="mt-auto">
        @if($partners && count($partners))
            <div class="mb-4">
                <p class="mb-2 text-sm text-gray-700">
                    @if (count($partners) === 1)
                        {{ __('In partnership with 1 organization', 'abbl') }}
                    @else
                        {{ sprintf(__('In partnership with %s organizations', 'abbl'), count($partners)) }}
                    @endif
                </p>
            </div>
        @endif
        <x-link-button href="{{ get_the_permalink() }}" size="sm" class="bg-neon-green">{{ __('See programme', 'abbl') }}</x-link-button>
    </div>
</article>
