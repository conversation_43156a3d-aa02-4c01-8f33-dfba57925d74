<?php

namespace App\Fields\MenuItems;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class PrimaryMenuItem extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('primary_menu_item');

        $fields
            ->setLocation('nav_menu_item', '==', 'location/professionals_primary')
            ->or('nav_menu_item', '==', 'location/consumers_primary');

        $fields
            ->addText('icon', [
                'label' => __('Icon', 'abbl-backend'),
                'instructions' => __('The FontAwesome icon to display. <br> For example: "fab fa-facebook". <br> This only works for sub-menu items !', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
