@props([
    'authorId' => get_the_author_meta('ID')
])

<div class="grid grid-cols-[auto_1fr] items-center gap-24 py-9 px-20 rounded-lg bg-abbl-gradient">
    <div class="overflow-hidden aspect-square w-[14rem] rounded-full">
        <img src="{{ Vite::asset('resources/images/needs-integration/author-avatar.jpg') }}" alt="">
    </div>
    <div>
        <p class="text-[1.125rem] font-bold text-neon-green">{{ get_the_author() }}</p>
        @if($job = get_field('author_job', 'user_'.$authorId))
            <p class="text-[1.125rem] text-white">{{ $job }}</p>
        @endif
        <p class="mt-4 text-white font-bold">Published on {{ get_the_date(ABBL_DATE_FORMAT_HUMAN) }}</p>
        <div class="flex items-center gap-7.5 mt-10">
            @if($linkedin_profile = get_field('author_linkedin_profile', 'user_'.$authorId))
                <a href="{{ $linkedin_profile }}" class="inline-flex items-center h-8 rounded-sm bg-white">
                                        <span class="inline-flex items-center h-full px-3 rounded-sm text-white bg-navy-blue">
                                            <i class="fab fa-linkedin-in"></i>
                                        </span>
                    <span class="px-3 text-[0.875rem] font-bold">LinkedIn Profile</span>
                </a>
            @endif
            <a href="#" class="text-white !underline">
                See all articles by {{ get_the_author() }}
            </a>
        </div>
    </div>
</div>
