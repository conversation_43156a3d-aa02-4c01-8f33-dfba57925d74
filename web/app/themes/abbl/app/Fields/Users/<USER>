<?php

namespace App\Fields\Users;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Author extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('author');

        $fields
            ->setLocation('user_form', '==', 'all')
                ->and('user_role', '!=', 'job_poster');

        $fields
            ->addText('author_job', [
                'label' => __('Job', 'abbl-backend'),
            ])
            ->addUrl('author_linkedin_profile', [
                'label' => __('Linkedin profile', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
