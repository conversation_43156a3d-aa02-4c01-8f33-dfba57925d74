<?php

namespace App\Fields\Templates;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class ProHomepage extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('pro_homepage');

        $fields
            ->setLocation('page_template', '==', 'template-homepage-pro.blade.php');

        $fields
            ->addGroup('intro')
                ->addText('tagline')
                ->addText('subtitle')
                ->addWysiwyg('content')
                ->addRepeater('key_figures')
                    ->addText('number')
                    ->addText('title')
                ->endRepeater()
                ->addLink('become_member_button')
                ->addLink('learn_more_button')
                ->addText('quick_access_title')
                ->addRepeater('quick_access_links')
                    ->addText('icon')
                    ->addLink('button')
                ->endRepeater()
            ->endGroup()
            ->addGroup('latest_news')
                ->addText('title')
                ->addNumber('max_news')
                ->addLink('bottom_button')
            ->endGroup()
            ->addGroup('events')
                ->addText('title')
                ->addLink('bottom_button')
            ->endGroup()
            ->addGroup('linkedin_feed')
                ->addText('title')
                ->addLink('bottom_button')
            ->endGroup()
            ->addGroup('publications')
                ->addText('title')
                ->addNumber('max_publications')
                ->addLink('bottom_button')
            ->endGroup();

        return $fields->build();
    }
}
