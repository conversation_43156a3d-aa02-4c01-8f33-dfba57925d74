{{--
    Template Name: Jobs index
--}}

@php use App\Modules\JobOffersModule; @endphp

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar
                aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', 'Carreers in luxembourg', get_the_title()]"/>
            </x-container>
        </x-section>

        <x-section>
            <x-container class="flex justify-between gap-4 p-4 bg-gray-300 ">
                <form class="flex gap-4" method="GET">
                    @php
                    $job_posters = abbl_get_job_posters();
                    $contract_types = get_terms(['taxonomy' => TAX_JOB_OFFER_CONTRACT_TYPE, 'hide_empty' => true]);
                    $job_offer_fields = get_terms(['taxonomy' => TAX_JOB_OFFER_FIELD, 'hide_empty' => true]);
                    @endphp

                    <select name="job-offer-job-poster" class="px-4 py-2 bg-gray-100" onchange="this.closest('form').submit();">
                        <option value="">{{ __('Company name', 'abbl') }}</option>
                        @foreach ($job_posters as $job_poster)
                            <option value="{{ $job_poster->ID }}" @if(get_query_var('job-offer-job-poster') == $job_poster->ID) selected @endif>
                                {{ get_field('company_name', $job_poster) }}
                            </option>
                        @endforeach
                    </select>

                    <select name="{{ TAX_JOB_OFFER_CONTRACT_TYPE }}" class="px-4 py-2 bg-gray-100" onchange="this.closest('form').submit();">
                        <option value="">{{ __('Contract type', 'abbl') }}</option>
                        @foreach ($contract_types as $contract_type)
                            <option value="{{ $contract_type->slug }}" @if(get_query_var(TAX_JOB_OFFER_CONTRACT_TYPE) == $contract_type->slug) selected @endif>
                                {{ $contract_type->name }}
                            </option>
                        @endforeach
                    </select>

                    <select name="{{ TAX_JOB_OFFER_FIELD }}" class="px-4 py-2 bg-gray-100" onchange="this.closest('form').submit();">
                        <option value="">{{ __('Field / domain', 'abbl') }}</option>
                        @foreach ($job_offer_fields as $job_offer_field)
                            <option value="{{ $job_offer_field->slug }}" @if(get_query_var(TAX_JOB_OFFER_FIELD) == $job_offer_field->slug) selected @endif>
                                {{ $job_offer_field->name }}
                            </option>
                        @endforeach
                    </select>
                </form>
                <button class="px-4 py-2 bg-gray-400">{{ __('Clear all filters', 'abbl') }}</button>
            </x-container>
        </x-section>

        <x-section>
            <x-container class="mb-16">
                @php
                $job_offers_query = JobOffersModule::query();
                @endphp

                @if($job_offers_query->have_posts())
                    <p class="mb-8 text-2xl font-bold">Available offers: {{ $job_offers_query->post_count }}</p>
                    <div class="grid grid-cols-3 gap-16">
                        @while($job_offers_query->have_posts())
                            @php $job_offers_query->the_post(); @endphp

                            <x-job-card/>
                        @endwhile
                    </div>
                @endif
            </x-container>
        </x-section>

        <x-section>
            <x-flexibles.newsletter
                title="Never miss a new job opportunity <br> Stay informed about the latest job openings in Luxembourg’s financial sector."
                content="<p>Subscribe to our newsletter</p>"
                :show-linkedin="true"
            />
        </x-section>

    @endwhile
@endsection
