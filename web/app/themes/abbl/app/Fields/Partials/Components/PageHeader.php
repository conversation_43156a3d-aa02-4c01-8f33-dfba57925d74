<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class PageHeader extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('page_header');

        $fields
            ->addText('page_header_title', [
                'label' => __('Title', 'abbl-backend'),
            ])
            ->addWysiwyg('page_header_content', [
                'label' => __('Content', 'abbl-backend'),
            ])
            ->addImage('page_header_image', [
                'label' => __('Image', 'abbl-backend'),
            ]);

        return $fields;
    }
}
