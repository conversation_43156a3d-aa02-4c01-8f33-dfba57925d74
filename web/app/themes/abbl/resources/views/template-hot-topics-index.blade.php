@php use App\Modules\HotTopicsModule; @endphp
{{--
    Template Name: Hot topics index
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', 'Hot topics']" />
            </x-container>
        </x-section>

        <x-section>
            @php
                $hot_topics_query = HotTopicsModule::query([
                    'posts_per_page' => -1,
                ]);
            @endphp

            <x-container class="grid gap-4">
                @while($hot_topics_query->have_posts())
                    @php
                    $hot_topics_query->the_post();

                    $category = get_the_terms(get_the_ID(), TAX_HOT_TOPIC_CATEGORY)[0] ?? null;
                    $is_second_post = $hot_topics_query->current_post === 1;
                    $is_last_post = $hot_topics_query->current_post === ($hot_topics_query->post_count - 1);
                    @endphp

                    {{-- Start the grid after the first post --}}
                    @if($is_second_post)
                        <div class="grid grid-cols-3 gap-4">
                    @endif

                    <article class="p-4 bg-gray-200">
                        @if($category)
                        <div class="flex mb-16">
                            <x-category-badge :category="$category" />
                        </div>
                       @endif
                        <div class="mt-auto">
                            <h3 class="mb-4 text-lg font-bold">{!! get_the_title() !!}</h3>
                            <p class="mb-8">{!! get_the_excerpt() !!}</p>
                            <x-link-button href="{{ get_the_permalink() }}">
                                {{ __('Read more', 'abbl') }}
                            </x-link-button>
                        </div>
                    </article>

                    {{-- Close the grid after the last post --}}
                    @if($hot_topics_query->post_count > 1 && $is_last_post)
                        </div>
                    @endif
                @endwhile
            </x-container>
        </x-section>

        <x-section>
            <x-flexibles.newsletter :content="false" />
        </x-section>
    @endwhile
@endsection
