{{--
  Template Name: Consumers homepage
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post() @endphp

        <x-featured-hot-topics-slider :postsCount="4"/>

        <x-section class="py-16 my-0 bg-navy-blue">
            <x-container class="grid grid-cols-[2fr_1fr] gap-16">
                <div class="space-y-4 pr-8 text-white">
                    <h2 class="mb-4 font-bold uppercase">ABBL - The Luxembourg’s Bankers’ Association</h2>
                    <p class="text-[1.25rem] font-bold text-neon-green">Empowering You with Knowledge, Protection and Confidence</p>
                    <div class="wysiwyg mb-20">
                        <p>The ABBL is committed to helping individuals better understand the banking world and feel confident in their financial decisions. This dedicated section of our website is here to support you—whether you're opening your first bank account, seeking to understand your rights as a client, or learning how to spot online fraud.</p>
                        <p>We offer trusted information on everyday banking products and services, explain the rights and protections you benefit from—including MiFID safeguards, deposit and investor compensation schemes—and raise awareness on how to protect your personal data and finances. You’ll also discover the work of the Fondation ABBL pour l’éducation financière, which brings financial literacy tools and training to young people, seniors, and vulnerable communities. Because strong financial knowledge empowers everyone.</p>
                    </div>
                    <div class="grid grid-cols-2 gap-8">
                        <x-link-button href="#" class="px-4">
                            {{ __('Discover our financial education tools', 'abbl') }}
                        </x-link-button>
                        <x-link-button href="#" class="px-4 bg-white">
                            {{ __(' Learn more about the Fondation ABBL', 'abbl') }}
                        </x-link-button>
                    </div>
                </div>
                <div class="py-18 px-14 rounded-lg bg-light-blue/15">
                    <h3 class="mb-8 text-[1.75rem] font-bold text-white">{{ __('Quick access', 'abbl') }}</h3>
                    <ul class="grid gap-5">
                        @php
                            $links = [
                                ['icon' => 'fas fa-bank', 'title' => 'Public Banking Holidays', 'url' => '#'],
                                ['icon' => 'fas fa-bookmark', 'title' => 'Publications', 'url' => '#'],
                                ['icon' => 'fas fa-user-tie', 'title' => 'Professional area', 'url' => '#'],
                                ['icon' => 'fas fa-briefcase', 'title' => 'Jobs & Careers', 'url' => '#'],
                            ];
                        @endphp

                        @foreach($links as $link)
                            <li>
                                <a href="{{ $link['url'] }}" class="flex items-center gap-4 pl-5 pr-2 py-4 rounded font-semibold bg-white">
                                    <i class="{{ $link['icon'] }}"></i>
                                    <span>{{ $link['title'] }}</span>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </x-container>
        </x-section>

        <div class="py-20 bg-abbl-gradient-revert">
            <x-section class="mt-0">
                <x-container>
                    <h2 class="mb-8 font-headings text-[1.75rem] text-light-blue font-bold">Our latest news</h2>

                    <x-news-slider />

                    <div class="mt-5">
                        <x-link-button href="{{ get_post_type_archive_link('post') }}">
                            {{ __('More news & insights', 'abbl') }}
                        </x-link-button>
                    </div>
                </x-container>
            </x-section>

            <x-section>
                <x-container>
                    <h2 class="mb-8 font-headings text-[1.75rem] text-light-blue font-bold">Our latest news</h2>

                    <ul class="grid grid-cols-5 gap-5">
                        @foreach(range(0, 4) as $i)
                            <li>
                                <article class="flex flex-col items-center h-full py-7 px-4.5 rounded-xl text-center bg-neon-green">
                                    <h3 class="mb-6 font-headings text-[1.125rem] font-bold text-center">Money Odyssey</h3>
                                    <img class="block w-[98px] mb-6" src="{{ Vite::asset('resources/images/needs-integration/thin-icon-percentage.svg') }}" alt="">
                                    <div class="mb-7 text-[0.875rem]">
                                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam eu dolor elit. Morbi ultrices condimentum lorem, eu sodales arcu pharetra ac. </p>
                                    </div>
                                    <div class="mt-auto">
                                        <x-link-button href="#" size="sm">Read more</x-link-button>
                                    </div>
                                </article>
                            </li>
                        @endforeach
                    </ul>
                </x-container>
            </x-section>

            <x-section>
                <x-container>
                    <div class="grid grid-cols-[7fr_5fr] gap-5">
                        <div>
                            <h2 class="mb-8 font-headings text-[1.75rem] text-light-blue font-bold">Frequent questions</h2>
                            <div class="grid gap-2.5">
                                @php
                                $faqs = [
                                    'Can I open a bank account in Luxembourg as a non resident?',
                                    'How do I protect myself from online fraud?',
                                    'Can I open a bank account in Luxembourg as a non resident?',
                                ];
                                @endphp

                                @foreach ($faqs as $i => $faq)
                                    @php $is_first = $i === 0; @endphp

                                    <details class="rounded bg-light-blue" @if($is_first) open @endif>
                                        <summary class="flex justify-between items-center p-4 rounded bg-neon-green cursor-pointer user-select-none">
                                            <h3 class="font-headings text-[1.125rem] font-bold">{{ $faq }}</h3>
                                            <i class="fas @if($is_first) fa-square-plus @else fa-square-minus @endif"></i>
                                        </summary>
                                        <div>
                                            <div class="p-4 pt-8">
                                                <div class="wysiwyg pb-8">
                                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque ullamcorper vestibulum ligula, nec ultrices dolor imperdiet a. Curabitur in accumsan odio. Ut maximus nulla vitae enim porttitor, nec egestas diam blandit.</p>
                                                    <p>Vestibulum convallis bibendum odio sit amet iaculis. Curabitur sit amet enim fermentum, fringilla lectus vel, mattis purus. Vestibulum elit arcu.</p>
                                                </div>
                                                <x-link-button href="#" size="sm">More on this topic</x-link-button>
                                            </div>
                                        </div>
                                    </details>
                                @endforeach
                            </div>
                        </div>
                        <div>
                            <h2 class="mb-8 font-headings text-[1.75rem] text-light-blue font-bold">Did you know?</h2>
                            <div class="pt-3 px-9 pb-8 rounded-lg text-center bg-yellow">
                                <p class="mb-2 text-[8rem] leading-normal font-extrabold">53%</p>
                                <p class="mb-10 text-[1.25rem] leading-[1.875rem] font-bold">Only 53% of Luxembourg adults reach the minimum score to be considered financially educated, according to an OECD study</p>
                                <x-link-button href="#" class="bg-white">Why financial education matters</x-link-button>
                            </div>
                        </div>
                    </div>
                </x-container>
            </x-section>

            <x-section>
                <x-container>
                    <h2 class="mb-8 font-headings text-[1.75rem] text-light-blue font-bold">Treasury trove: useful reads & tools</h2>
                    <div class="grid grid-cols-3 gap-5">
                        @foreach(range(0, 2) as $i)
                            <div class="rounded-lg bg-light-blue">
                                @php $cover_url = Vite::asset('resources/images/needs-integration/news-card-cover.jpg'); @endphp

                                <div class="relative overflow-hidden flex flex-col min-h-[242px] py-5 px-6 rounded-lg">
                                    <div class="absolute inset-0 bottom-[30%] bg-neon-green bg-cover bg-center" @if($cover_url) style="background-image: url('{{ $cover_url }}') @endif"></div>
                                    <div class="absolute block inset-0 bg-linear-to-b from-transparent from-25% via-neon-green via-60% to-neon-green to-100%"></div>

                                    <div class="relative flex items-center gap-2 mb-auto">
                                        <x-badge>Market regulations</x-badge>
                                        @if($i === 0)
                                            <x-badge class="bg-yellow">Recommended</x-badge>
                                        @endif
                                    </div>

                                    <h3 class="relative font-headings leading-[1.25] font-bold">Bank account opening: dedicated contacts for AIFs, FinTechs and SMEs</h3>
                                </div>

                                <div class="pt-8 px-6 pb-6">
                                    <p class="mb-5 text-[0.875rem]">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut finibus dui ex, in egestas nisl vulputate in. Nulla non ipsum non nisi ornare pellentesque. Nullam eu neque aliquet, mollis metus ut, convallis enim.</p>
                                    <p class="mb-4 text-[0.6875rem]">Source : febelfin.be</p>
                                    <x-link-button href="#" size="sm" class="bg-neon-green">Read more</x-link-button>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="mt-10">
                        <x-link-button>See more resources</x-link-button>
                    </div>
                </x-container>
            </x-section>

            <x-section class="mb-0">
                <x-container>
                    <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-light-blue font-bold">
                        {{ __('Consumer publications', 'abbl') }}
                    </h2>

                    @php
                        $publications_query = new WP_query([
                            'post_type' => CPT_PUBLICATION,
                            'posts_per_page' => 6,
                        ]);
                    @endphp

                    <div id="home-publications-slider" class="swiper-container with-fade">
                        <!-- Slider main container -->
                        <div class="swiper">
                            <!-- Additional required wrapper -->
                            <div class="swiper-wrapper">
                                <!-- Slides -->
                                @while($publications_query->have_posts())
                                    @php $publications_query->the_post(); @endphp

                                    <div class="swiper-slide">
                                        <x-publication-card :featured="$publications_query->current_post === 0" @class(['h-full', 'border border-neon-green glow-neon-green' => $publications_query->current_post === 0, 'relative z-10' => $publications_query->current_post > 0]) />
                                    </div>
                                @endwhile
                            </div>
                        </div>

                        <div class="swiper-pagination"></div>

                        <!-- If we need navigation buttons -->
                        <div class="swiper-button-prev">
                            <i class="fas fa-square-chevron-left text-yellow"></i>
                        </div>
                        <div class="swiper-button-next">
                            <i class="fas fa-square-chevron-right text-yellow"></i>
                        </div>
                    </div>

                    <div class="relative z-10 mt-5">
                        <x-link-button href="#">{{ __('See all publications', 'abbl') }}</x-link-button>
                    </div>
                </x-container>
            </x-section>
        </div>
    @endwhile
@endsection
