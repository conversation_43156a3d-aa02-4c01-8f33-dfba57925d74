<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class Quote extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('quote');

        $fields
            ->addText('quote_author_name', [
                'label' => __('Author name', 'abbl-backend'),
            ])
            ->addText('quote_author_job', [
                'label' => __('Author job', 'abbl-backend'),
            ])
            ->addImage('quote_author_image', [
                'label' => __('Image', 'abbl-backend'),
            ])
            ->addWysiwyg('quote_content', [
                'label' => __('Content', 'abbl-backend'),
            ]);

        return $fields;
    }
}
