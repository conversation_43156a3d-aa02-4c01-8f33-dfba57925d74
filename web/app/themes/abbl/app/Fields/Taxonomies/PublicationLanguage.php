<?php

namespace App\Fields\Taxonomies;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class PublicationLanguage extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('publication_language');

        $fields
            ->setLocation('taxonomy', '==', TAX_PUBLICATION_LANGUAGE);

        $fields
            ->addText('code', [
                'label' => __('Code', 'abbl-backend'),
                'instructions' => __('For example FR, EN, DE, ...', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
