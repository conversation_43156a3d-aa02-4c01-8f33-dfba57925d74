<?php

namespace App\Fields\PostTypes;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class JobOffer extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('job_offer');

        $fields
            ->setLocation('post_type', '==', CPT_JOB_OFFER);

        $fields
            ->addWysiwyg('description', [
                'label' => __('Description', 'abbl-backend'),
            ])
            ->addDatePicker('closes_at', [
                'label' => __('Closes at', 'abbl-backend'),
            ])
            ->addText('apply_title', [
                'label' => __('Apply title', 'abbl-backend'),
                'default_value' => __('Apply', 'abbl-backend'),
            ])
            ->addWysiwyg('apply_content', [
                'label' => __('Apply content', 'abbl-backend'),
            ])
            ->addLink('apply_button_url', [
                'label' => __('Apply button URL', 'abbl-backend'),
                'default_value' => [
                    'url' => null,
                    'title' => __('Click here to apply', 'abbl-backend'),
                ],
            ]);

        return $fields->build();
    }
}
