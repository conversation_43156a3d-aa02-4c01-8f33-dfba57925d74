<?php

namespace App\Fields\PostTypes\JobOffer;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class JobOfferPrivateFields extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('job_offer_private_fields', [
            'title' => __('Private fields', 'abbl-backend'),
            'position' => 'side',
        ]);

        $fields
            ->setLocation('post_type', '==', CPT_JOB_OFFER)
                ->and('current_user_role', '==', 'administrator')
                ->or('current_user_role', '==', 'editor');

        $fields
            ->addTrueFalse('is_abbl_job', [
                'label' => __('Is an ABBL job', 'abbl-backend'),
                'default_value' => false,
            ]);

        return $fields->build();
    }
}
