<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class CenteredImage extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('centered_image');

        $fields
            ->addImage('centered_image_image', [
                'label' => __('Image', 'abbl-backend'),
            ])
            ->addWysiwyg('centered_image_caption', [
                'label' => __('Caption', 'abbl-backend'),
            ]);

        return $fields;
    }
}
