@props([
    'postsCount' => 3
])

<x-section class="relative min-h-[471px] my-0 pt-20 pb-12 lg:py-12">
    <div class="absolute inset-0 bg-cover bg-y-top bg-x-center" style="background-image: url('{{  Vite::asset('resources/images/needs-integration/hot-topics-slider-bg.jpg') }}')"></div>
    <div class="absolute inset-0 bg-navy-blue/75"></div>
    <div class="absolute inset-0 left-0 w-[66%] bg-linear-to-r from-navy-blue/75 to-transparent"></div>

    <div id="home-hot-topics-slider" class="swiper-container with-fade">
        <x-container class="relative">
            <!-- Slider main container -->
            <div class="swiper">
                <!-- Additional required wrapper -->
                <div class="swiper-wrapper">
                    @php
                        $hot_topics_query = new WP_Query([
                            'post_type' => CPT_HOT_TOPIC,
                            'posts_per_page' => $postsCount,
                        ]);

                        $i = 0;
                    @endphp

                    @while($hot_topics_query->have_posts())
                        @php
                            $hot_topics_query->the_post();

                            $category = get_the_terms(get_the_ID(), TAX_HOT_TOPIC_CATEGORY)[0] ?? null;
                        @endphp

                        <div class="swiper-slide">
                            <div>
                                <article class="flex flex-col p-4 lg:pr-[465px]">
                                    <div class="mb-8">
                                        @if ($category)
                                            <x-category-badge :category="$category" />
                                        @endif
                                    </div>
                                    <div class="mt-auto">
                                        <h3 class="mb-4 text-[1.25rem] font-headings font-bold text-neon-green">{{ get_the_title() }}</h3>
                                        <div class="mb-14 text-white">
                                            <p>{{ get_the_excerpt() }}</p>
                                        </div>
                                        <x-link-button href="{{ get_the_permalink() }}">
                                            {{ __('Explore this topic', 'abbl') }}
                                        </x-link-button>
                                    </div>
                                </article>
                            </div>
                        </div>

                        @php $i++; @endphp
                    @endwhile

                    @php
                        wp_reset_postdata();
                    @endphp
                </div>
            </div>

            <!-- If we need navigation buttons -->
            <div class="hidden lg:block swiper-button-prev">
                <i class="fas fa-square-chevron-left text-neon-green"></i>
            </div>
            <div class="hidden lg:block swiper-button-next">
                <i class="fas fa-square-chevron-right text-neon-green"></i>
            </div>

            <div class="z-10 lg:absolute lg:top-0 lg:right-0 lg:w-[345px] p-6 rounded-lg bg-white">
                <h2 class="pb-4 text-[1.125rem] font-headings font-bold">{{ __("What's trending in the banking landscape", 'abbl') }}</h2>

                @php $i = 0; @endphp

                <ul id="slider-hot-topics-list">
                    @while($hot_topics_query->have_posts())
                        @php
                            $hot_topics_query->the_post();
                        @endphp

                        <li
                            data-index="{{ $hot_topics_query->current_post }}"
                            @class([
                                'py-3 border-t border-neon-green last-of-type:border-b text-[0.8125rem] cursor-pointer',
                                'font-bold' => $i === 0,
                            ])
                        >
                            <article>
                                <h3>{{ get_the_title() }}</h3>
                            </article>
                        </li>

                        @php $i++; @endphp
                    @endwhile

                    @php wp_reset_postdata(); @endphp
                </ul>
                <div class="mt-7">
                    <x-link-button href="{{ get_post_type_archive_link(CPT_HOT_TOPIC) }}" size="sm" class="bg-neon-green">
                        {{ __('See all hot topics', 'abbl') }}
                    </x-link-button>
                </div>
            </div>

            <div
                class="absolute -right-[22.4375rem] -bottom-[11.25rem] w-[15.8125rem] h-[18.375rem] hidden lg:block point-events-none bg-contain bg-center"
                style="background-image: url('{{ Vite::asset('resources/images/decorations/yellow-candles.svg') }}')"
            ></div>
        </x-container>
    </div>
</x-section>
