{{--
    Template Name: Newsletter pro
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-flexibles.newsletter />
        </x-section>

        <x-section>
            <x-container class="wysiwyg">
                <h2>What you will find in ABBL’s newsletter</h2>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                <p>Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
            </x-container>
        </x-section>

        <x-section>
            <x-container>
                <h2 class="mb-8 text-xl font-bold">Our latest newsletters</h2>
                <ul>
                    @foreach(range(0, 8) as $i)
                        <li>
                            <article class="flex items-center gap-4 py-4 px-8  @if($loop->odd) bg-gray-100 @endif">
                                <h3 class="font-bold">ABBL Newsletter June 2025</h3>
                                <p class="mr-auto">06/20/2025</p>
                                <a class="inline-block px-4 py-2 bg-gray-400" href="#">View newsletter</a>
                            </article>
                        </li>
                    @endforeach
                </ul>
            </x-container>
        </x-section>
    @endwhile
@endsection
