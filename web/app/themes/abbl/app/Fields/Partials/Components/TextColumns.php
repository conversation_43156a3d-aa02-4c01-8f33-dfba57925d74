<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class TextColumns extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('text_columns');

        $fields
            ->addRepeater('rows', [
                'label' => __('Row', 'abbl-backend'),
            ])
                ->addRepeater('columns', [
                    'label' => __('Column', 'abbl-backend'),
                    'max' => 4,
                ])
                    ->addWysiwyg('content')
                ->endRepeater()
            ->endRepeater();

        return $fields;
    }
}
