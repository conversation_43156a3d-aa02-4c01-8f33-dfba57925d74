<?php

namespace App\Fields\PostTypes;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class TeamMember extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('team_member');

        $fields
            ->setLocation('post_type', '==', CPT_TEAM_MEMBER);

        $fields
            ->addImage('portrait', [
                'label' => __('Portrait', 'abbl-backend'),
            ])
            ->addText('job_title', [
                'label' => __('Job title', 'abbl-backend'),
            ])
            ->addWysiwyg('short_bio', [
                'label' => __('Short bio', 'abbl-backend'),
            ])
            ->addTrueFalse('highlight_job_title', [
                'label' => __('Highlight job title', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addUrl('linkedin_url', [
                'label' => __('Linkedin URL', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
