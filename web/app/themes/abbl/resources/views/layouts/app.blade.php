<!doctype html>
<html @php(language_attributes())>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    @php(do_action('get_header'))
    @php(wp_head())

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Funnel+Display:wght@300..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

    @stack('head-first-scripts')

    <script src="https://kit.fontawesome.com/c450bd9af1.js" crossorigin="anonymous"></script>

    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body @php(body_class())>
@php(wp_body_open())

<div id="app">
    <a class="sr-only focus:not-sr-only" href="#main">
        {{ __('Skip to content', 'abbl') }}
    </a>

    @if (get_page_template_slug() === 'template-homepage.blade.php')
        <header class="flex justify-center py-6 md:py-12">
            <img class="block w-[6.5625rem]" src="{{ Vite::asset('resources/images/abbl-logo-blue.svg') }}" alt="Logo ABBL">
        </header>
    @else
        @include('sections.header')
    @endif

    <main id="main" class="main">
        @yield('content')
    </main>

    @hasSection('sidebar')
        <aside class="sidebar">
            @yield('sidebar')
        </aside>
    @endif

    @include('sections.footer')
</div>

@php(do_action('get_footer'))
@php(wp_footer())

@stack('footer-scripts')
</body>
</html>
