<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class Takeaways extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('takeaways');

        $fields
            ->addText('takeaways_title', [
                'label' => __('Title', 'abbl-backend'),
            ])
            ->addWysiwyg('takeaways_content', [
                'label' => __('Content', 'abbl-backend'),
                'instructions' => __('Use a bullet list.', 'abbl-backend'),
            ]);

        return $fields;
    }
}
