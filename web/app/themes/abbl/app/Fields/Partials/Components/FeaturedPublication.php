<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;
use const App\CPT_PUBLICATION;

class FeaturedPublication extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('featured_publication');

        $fields
            ->addPostObject('featured_publication_post', [
                'label' => __('Publication', 'abbl-backend'),
                'post_type' => CPT_PUBLICATION,
            ]);

        return $fields;
    }
}
