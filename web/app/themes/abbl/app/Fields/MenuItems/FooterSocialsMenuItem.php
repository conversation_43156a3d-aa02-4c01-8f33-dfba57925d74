<?php

namespace App\Fields\MenuItems;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class FooterSocialsMenuItem extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('footer_socials_menu_item');

        $fields
            ->setLocation('nav_menu_item', '==', 'location/footer_socials');

        $fields
            ->addText('icon', [
                'label' => __('Icon', 'abbl-backend'),
                'instructions' => __('The FontAwesome icon to display. For example: "fab fa-facebook"', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
