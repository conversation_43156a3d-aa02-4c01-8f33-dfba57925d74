{{--
  Template Name: News index
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
            the_post();
        @endphp

        <x-flexibles.hero title="ABBL News">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar
                aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', 'News']"/>
            </x-container>
        </x-section>

        <x-section>
            <x-container class="mb-8">
                <form method="GET">
                    <x-archive-filters>
                        @php
                            $topics = get_terms([
                                'post_type' => 'post',
                                'taxonomy' => 'category',
                             ]);

                            $types_of_content = get_terms([
                                'post_type' => 'post',
                                'taxonomy' => TAX_POST_CONTENT_TYPE,
                            ]);

                            $authors = get_users();

                            $publication_dates = abbl_get_publication_dates();
                        @endphp

                        <x-select>
                            <option value="">Topic</option>
                            @foreach ($topics as $topic)
                                <option value="{{ $topic->term_id }}">{{ $topic->name }}</option>
                            @endforeach
                        </x-select>

                        <x-select>
                            <option value="">Type of content</option>
                            @foreach ($types_of_content as $content)
                                <option value="{{ $content->term_id }}">{{ $content->name }}</option>
                            @endforeach
                        </x-select>

                        <x-select>
                            <option value="">Publication date</option>
                            @foreach ($publication_dates as $date)
                                <option
                                    value="{{ $date->year }}-{{ $date->month }}">{{ $date->monthName }} {{ $date->year }}</option>
                            @endforeach
                        </x-select>

                        <x-select>
                            <option value="">Author</option>
                            @foreach ($authors as $author)
                                <option value="{{ $author->ID }}">{{ $author->display_name }}</option>
                            @endforeach
                        </x-select>

                        <x-button type="submit" class="w-full lg:w-auto ml-auto">{{ __('Clear all filters', 'abbl') }}</x-button>
                    </x-archive-filters>
                </form>
            </x-container>

            @php
                $posts_per_page = 12;
                $news_query = new WP_Query([
                    'post_type' => 'post',
                    'posts_per_page' => $posts_per_page,
                ]);

                $i = 0;
            @endphp

            <x-container class="mb-8">
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @while($news_query->have_posts())
                        @php $news_query->the_post(); @endphp

                        {{-- Insert the newsletter aside when we're halfway through --}}
                        @if($i === $posts_per_page / 2)
                </div>

                <div class="my-8">
                    <x-flexibles.newsletter/>
                </div>

                <div class="grid grid-cols-3 gap-4">
                    @endif

                    <x-news-card/>

                    @php $i++; @endphp
                    @endwhile
                </div>
            </x-container>

            <div class="container mx-auto mb-8">
                @php the_posts_navigation() @endphp
            </div>
        </x-section>
    @endwhile
@endsection
