@props([
    'id' => get_the_ID(),
])

@php
$category = get_the_terms($id, 'category')[0] ?? null;
$cover_url = get_the_post_thumbnail_url(post: $id, size: 'full');

$is_featured = get_field('is_featured', $id);
@endphp

<a href="{{ get_the_permalink($id) }}" class="overflow-hidden relative flex flex-col min-h-[242px] py-5 px-6 rounded-lg outline-yellow -outline-offset-4 focus-visible:outline-4 ">
    <div class="-z-10 absolute inset-0 bottom-[30%] bg-neon-green bg-cover bg-center" @if($cover_url) style="background-image: url('{{ $cover_url }}'); @endif"></div>
    <div class="-z-10 absolute block inset-0 bg-linear-to-b from-transparent from-25% via-neon-green via-60% to-neon-green to-100%"></div>

    <div class="relative flex items-center gap-2 mb-auto">
        @if ($category)
            <x-category-badge :category="$category" />
        @endif
        @if ($is_featured)
            <x-badge class="bg-yellow">Featured</x-badge>
        @endif
    </div>
    <p class="relative mb-2 text-[0.625rem] font-bold">{{ get_the_date(ABBL_DATE_FORMAT_HUMAN, $id) }}</p>
    <h3 class="relative font-headings leading-[1.25] font-bold">{!! get_the_title($id) !!}</h3>
</a>
