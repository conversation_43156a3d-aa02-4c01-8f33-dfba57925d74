<?php

namespace App\Fields\PostTypes;

use App\Fields\Partials\Components\BigNumber;
use App\Fields\Partials\Components\CenteredImage;
use App\Fields\Partials\Components\Quote;
use App\Fields\Partials\Components\RelatedNews;
use App\Fields\Partials\Components\RelatedPublications;
use App\Fields\Partials\Components\TextAndBigNumber;
use App\Fields\Partials\Components\TextColumns;
use App\Fields\Partials\Components\TextContent;
use App\Fields\Partials\Components\TextTwoColumns;
use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Post extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('post');

        $fields
            ->setLocation('post_type', '==', 'post');

        $fields
            ->addTrueFalse('is_featured', [
                'label' => __('Featured', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addTrueFalse('has_takeaways', [
                'label' => __('Has takeaways', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addWysiwyg('takeaways_content', [
                'label' => __('Takeaways content', 'abbl-backend'),
            ])
                ->conditional('has_takeaways', '==', 1)
            ->addFlexibleContent('sections')
                ->addLayout('text_columns_layout', [
                    'label' => __('Text columns', 'abbl-backend'),
                ])
                    ->addPartial(TextColumns::class)
                ->addLayout('text_two_columns_layout', [
                    'label' => __('Text two columns', 'abbl-backend'),
                ])
                    ->addPartial(TextTwoColumns::class)
                ->addLayout('centered_image_layout', [
                    'label' => __('Centered image', 'abbl-backend'),
                ])
                    ->addPartial(CenteredImage::class)
                ->addLayout('text_content_layout', [
                    'label' => __('Text content', 'abbl-backend'),
                ])
                    ->addPartial(TextContent::class)
                ->addLayout('quote_layout', [
                    'label' => __('Quote', 'abbl-backend'),
                ])
                    ->addPartial(Quote::class)
                ->addLayout('related_publications_layout', [
                    'label' => __('Related publications', 'abbl-backend'),
                ])
                    ->addPartial(RelatedPublications::class)
                ->addLayout('related_news_layout', [
                    'label' => __('Related news', 'abbl-backend'),
                ])
                    ->addPartial(RelatedNews::class)
                ->addLayout('text_and_big_number_layout', [
                    'label' => __('Text and big number', 'abbl-backend'),
                ])
                    ->addPartial(TextAndBigNumber::class)
            ->endFlexibleContent();

        return $fields->build();
    }
}
