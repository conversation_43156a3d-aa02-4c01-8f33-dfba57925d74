<?php

namespace App\Fields\Taxonomies;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class JobCompany extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('job_company');

        $fields
            ->setLocation('taxonomy', '==', TAX_JOB_OFFER_COMPANY);

        $fields
            ->addImage('logo', [
                'label' => __('Logo', 'abbl-backend'),
            ])
            ->addWysiwyg('description', [
                'label' => __('Description', 'abbl-backend'),
            ])
            ->addUrl('website_url', [
                'label' => __('Website URL', 'abbl-backend'),
            ])
            ->addUrl('linkedin_url', [
                'label' => __('Linkedin URL', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
