<?php

namespace App\Fields\Taxonomies;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class EventVenue extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('event_venue');

        $fields
            ->setLocation('taxonomy', '==', TAX_EVENT_VENUE);

        $fields
            ->addWysiwyg('address', [
                'label' => __('Address', 'abbl-backend'),
            ])
            ->addGoogleMap('coordinates', [
                'label' => __('Coordinates', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
