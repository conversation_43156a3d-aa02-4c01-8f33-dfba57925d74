{{--
    Template Name: Professionals homepage
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-featured-hot-topics-slider/>

        @while(have_rows('intro'))
            @php the_row(); @endphp

            <x-section class="py-16 my-0 bg-light-blue">
                <x-container class="relative grid lg:grid-cols-[2fr_1fr] lg:items-center lg:gap-24">
                    <div class="space-y-4">
                        @if($tagline = get_sub_field('tagline'))
                            <h2 class="mb-2.5 text-[0.9375rem] font-bold uppercase">{{ $tagline }}</h2>
                        @endif
                        @if($subtitle = get_sub_field('subtitle'))
                            <p class="text-[1.25rem] font-headings font-bold">{{ $subtitle }}</p>
                        @endif
                        @if($content = get_sub_field('content'))
                            <div class="wysiwyg">
                                {!! $content !!}
                            </div>
                        @endif

                        @if(have_rows('key_figures'))
                            @php
                                $key_figure_count = count(get_field('intro_key_figures'));
                            @endphp

                            <ul class="flex justify-between lg:justify-start lg:items-center lg:gap-14 my-12">
                                @while(have_rows('key_figures'))
                                    @php the_row(); @endphp

                                    <div>
                                        @if($number = get_sub_field('number'))
                                            <p class="text-[1.5rem] leading-none font-bold">{{ $number }}</p>
                                        @endif
                                        @if($title = get_sub_field('title'))
                                            <p class="text-[0.875rem] uppercase">{{ $title }}</p>
                                        @endif
                                    </div>
                                    @if(!(get_row_index() >= $key_figure_count))
                                        <div class="w-0.5 h-8 bg-navy-blue"></div>
                                    @endif
                                @endwhile
                            </ul>
                        @endif

                        <div class="grid grid-cols-2 lg:flex gap-8">
                            @if($button = get_sub_field('become_member_button'))
                                <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}">
                                    {{ $button['title'] }}
                                </x-link-button>
                            @endif
                            @if($button = get_sub_field('learn_more_button'))
                                <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}" class="bg-white">
                                    {{ $button['title'] }}
                                </x-link-button>
                            @endif
                        </div>
                    </div>
                    <div class="py-18 px-14 rounded-lg bg-navy-blue">
                        @if($quick_access_title = get_sub_field('quick_access_title'))
                            <h3 class="mb-8 text-[1.75rem] font-bold text-neon-green">{{ $quick_access_title }}</h3>
                        @endif

                        @if(have_rows('quick_access_links'))
                            <ul class="grid gap-5">
                                @while(have_rows('quick_access_links'))
                                    @php the_row(); @endphp

                                    @if($button = get_sub_field('button'))
                                        <li>
                                            <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}" class="flex items-center justify-start gap-4 pl-5 pr-2 py-4 rounded font-medium bg-white outline-yellow">
                                                @if($icon = get_sub_field('icon'))
                                                    <i class="{{ $icon }}"></i>
                                                @endif
                                                <span>{{ $button['title'] }}</span>
                                            </x-link-button>
                                        </li>
                                    @endif
                                @endwhile
                            </ul>
                        @endif
                    </div>
                </x-container>
            </x-section>
        @endwhile

        <div class="py-20 bg-abbl-gradient-grain">
            @while(have_rows('latest_news'))
                @php the_row(); @endphp

                <x-section class="mt-0">
                    <x-container>
                        @if($latest_news_title = get_sub_field('title'))
                            <h2 class="mb-8 font-headings text-[1.75rem] text-white font-bold">
                                {{ $latest_news_title }}
                            </h2>
                        @endif

                        <x-news-slider :postsPerPage="get_sub_field('max_news') ?: 6" />

                        <div class="flex justify-center lg:justify-start mt-5">
                            @if($latest_news_button = get_sub_field('bottom_button'))
                                <x-link-button href="{{ $latest_news_button['url'] }}" target="{{ $latest_news_button['target'] ?? '_self' }}">
                                    {!! $latest_news_button['title'] !!}
                                </x-link-button>
                            @endif
                        </div>
                    </x-container>
                </x-section>
            @endwhile

            <x-section>
                <x-container class="relative grid lg:grid-cols-[2fr_1fr] lg:gap-16">
                    {{-- START EVENTS --}}
                    @while(have_rows('events'))
                        @php the_row(); @endphp

                        <div>
                            @if($events_title = get_sub_field('title'))
                                <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-white font-bold">{{ $events_title }}</h2>
                            @endif

                            @php
                            $events_query = new WP_Query([
                                'post_type' => CPT_EVENT,
                                'posts_per_page' => get_sub_field('max_events') ?: 3,
                            ]);

                            $i = 0;
                            @endphp

                            @if($events_query->have_posts())
                                <div id="events-grid" class="grid lg:grid-cols-2 gap-4">
                                    @while($events_query->have_posts())
                                        @php
                                            $events_query->the_post();
                                            $is_featured = get_field('is_featured');
                                        @endphp

                                        <x-event-card
                                            @class([
                                                'glow-neon-green border border-neon-green lg:col-span-2' => $is_featured,
                                                'relative z-10' => !$is_featured,
                                            ])
                                        />

                                        @php $i++; @endphp
                                    @endwhile

                                    @php wp_reset_postdata(); @endphp
                                </div>
                                <div class="mt-14">
                                    @if($button = get_sub_field('bottom_button'))
                                        <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}">
                                            {{ $button['title'] }}
                                        </x-link-button>
                                    @endif
                                </div>
                            @else
                                <p>{{ __('No upcoming events.') }}</p>
                            @endif

                            @php wp_reset_postdata(); @endphp
                        </div>
                    @endwhile
                    {{-- END EVENTS --}}

                    @while(have_rows('linkedin_feed'))
                        @php the_row(); @endphp

                        {{-- START LINKEDIN FEED --}}
                        <div>
                            @if($linkedin_title = get_field('linkedin_feed_title'))
                                <h2 class="mb-8 font-headings text-[1.75rem] text-white font-bold">{{ $linkedin_title }}</h2>
                            @endif
                            <div id="linkedin-feed" class="overflow-y-auto space-y-4 p-4 rounded-lg bg-light-blue">
                                @foreach(range(0, 3) as $i)
                                    <div class="px-5 py-4 rounded-lg text-black bg-white">
                                        <div class="flex items-center gap-4 mb-4">
                                            <div class="aspect-square flex justify-center items-center w-14 p-2 rounded-full bg-abbl-gradient">
                                                <img src="{{ Vite::asset('resources/images/abbl-logo.svg') }}" alt="">
                                            </div>
                                            <div>
                                                <h3 class="font-bold">ABBL</h3>
                                                <p class="text-[0.875rem]">Published on 10.20.2025</p>
                                            </div>
                                            <div class="ml-auto self-start">
                                                <i class="fab fa-linkedin text-[1.5rem] text-[#1E66BF]"></i>
                                            </div>
                                        </div>
                                        <div class="mb-6 space-y-[0.875rem] text-[0.875rem]">
                                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer mattis massa quis orci congue laoreet. Nam eu nibh id tortor placerat venenatis in ac quam. Integer nec est enim.</p>
                                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer mattis massa quis orci congue laoreet.</p>
                                        </div>
                                        <div>
                                            <img src="{{ Vite::asset('resources/images/needs-integration/linkedin-feed-image.jpg') }}" alt="" class="rounded">
                                        </div>
                                        <div class="h-px my-5 bg-[#EAEAEA]"></div>
                                        <div class="flex text-[0.875rem] [&>*]:flex [&>*]:items-center [&>*]:gap-2">
                                            <a href="#" class="mr-7 text-[#8E8E8E]">
                                                <i class="fas fa-heart text-[1rem]"></i>
                                                <span>468</span>
                                            </a>
                                            <a href="#" class="text-[#8E8E8E]">
                                                <i class="fab fa-rocketchat text-[1rem]"></i>
                                                <span>12</span>
                                            </a>
                                            <a href="#" class="ml-auto">
                                                <span>Share</span>
                                                <i class="fas fa-share text-[1rem]"></i>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="flex justify-center lg:justify-start mt-14">
                                @if($button = get_sub_field('bottom_button'))
                                    <x-link-button href="{{ $button['url'] }}" target="{{ $button['target'] ?? '_self' }}">
                                        {{ $button['title'] }}
                                    </x-link-button>
                                @endif
                            </div>
                        </div>
                        {{-- END LINKEDIN FEED --}}
                    @endwhile

                    <div
                        class="absolute -left-[24.4375rem] top-[11.25rem] w-[12.375rem] h-[16.3125rem] hidden lg:block point-events-none bg-fit bg-center"
                        style="background-image: url('{{ Vite::asset('resources/images/decorations/percentage.svg') }}')"
                    ></div>
                </x-container>
            </x-section>

            @while(have_rows('publications'))
                @php the_row(); @endphp

                {{-- START PUBLICATIONS --}}
                <x-section class="mb-0">
                    <x-container>
                        @if($title = get_sub_field('title'))
                            <h2 class="relative z-10 mb-8 font-headings text-[1.75rem] text-white font-bold">
                                {{ $title }}
                            </h2>
                        @endif

                        @php
                        $publications_query = new WP_query([
                            'post_type' => CPT_PUBLICATION,
                            'posts_per_page' => get_sub_field('max_publications') ?: 6,
                        ]);
                        @endphp

                        <div id="home-publications-slider" class="swiper-container with-fade">
                            <!-- Slider main container -->
                            <div class="swiper">
                                <!-- Additional required wrapper -->
                                <div class="swiper-wrapper">
                                    <!-- Slides -->
                                    @while($publications_query->have_posts())
                                        @php $publications_query->the_post(); @endphp

                                        <div class="swiper-slide">
                                            <x-publication-card :featured="$publications_query->current_post === 0" @class(['h-full', 'border border-neon-green glow-neon-green' => $publications_query->current_post === 0, 'relative z-10' => $publications_query->current_post > 0]) />
                                        </div>
                                    @endwhile
                                </div>
                            </div>

                            <div class="swiper-pagination"></div>

                            <!-- If we need navigation buttons -->
                            <div class="swiper-button-prev">
                                <i class="fas fa-square-chevron-left text-neon-green"></i>
                            </div>
                            <div class="swiper-button-next">
                                <i class="fas fa-square-chevron-right text-neon-green"></i>
                            </div>
                        </div>

                        <div class="relative z-10 flex justify-center lg:justify-start mt-5">
                            @if($publications_button = get_sub_field('bottom_button'))
                                <x-link-button href="{{ $publications_button['url'] }}" target="{{ $publications_button['target'] ?? '_self' }}">
                                    {{ $publications_button['title'] }}
                                </x-link-button>
                            @endif
                        </div>
                    </x-container>
                </x-section>
                {{-- END PUBLICATIONS --}}
            @endwhile
        </div>
    @endwhile
@endsection

@push('footer-scripts')
    <script>
        const eventsGrid = document.querySelector('#events-grid');
        const linkedinFeed = document.querySelector('#linkedin-feed');
        const feedImages = Array.from(linkedinFeed.querySelectorAll('img'));

        function resizeLinkedInFeedHeight() {
            const eventsGridHeight = eventsGrid.getBoundingClientRect().height;
            linkedinFeed.style.height = `${eventsGridHeight}px`;
        }

        resizeLinkedInFeedHeight();

        window.addEventListener('resize', () => resizeLinkedInFeedHeight());

        feedImages.forEach(img => {
            img.addEventListener('load', () => resizeLinkedInFeedHeight());
        });

        document.addEventListener('load', () => resizeLinkedInFeedHeight());
        document.addEventListener('DOMContentLoaded', () => resizeLinkedInFeedHeight());
    </script>
@endpush
