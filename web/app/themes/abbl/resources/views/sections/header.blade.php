{{--
@if($message = get_field('alert_banner_message', 'options'))
    <aside class="bg-gray-700">
        <x-container>
            <div class="flex justify-center items-center gap-4 py-4 text-white">
                @if($icon = get_field('alert_banner_icon', 'options'))
                    <i class="{{ $icon }}"></i>
                @endif
                <div class="wysiwyg">
                    {!! $message !!}
                </div>
            </div>
        </x-container>
    </aside>
@endif
--}}

<header>
    <x-container class="hidden lg:flex justify-between">
        <nav class="flex mt-auto" aria-label="{{ __('Consumer / Professionals navigation', 'abbl') }}">
            <a
                id="header-professionals-button"
                href="{{ abbl_professionals_home_url() }}"
                class="px-6 py-2 rounded-t text-[0.75rem] font-medium bg-navy-blue text-neon-green"
            >{{ __('Professionals', 'abbl') }}</a>
            <a
                id="header-consumers-button"
                href="{{ abbl_consumers_home_url() }}"
                class="px-6 py-2 rounded-t text-[0.75rem] font-medium bg-neon-green text-navy-blue"
            >{{ __('Consumers', 'abbl') }}</a>
        </nav>
        <nav aria-label="{{ __('Utilities navigation', 'abbl') }}">
            <ul class="flex gap-10 py-4">
                @php
                    $items = [
                        ['label' => __('Search', 'abbl'), 'icon' => 'fa-search', 'url' => '#'],
                        ['label' => __('Log in', 'abbl'), 'icon' => 'fa-user', 'url' => '#'],
                    ];
                @endphp

                @foreach($items as $item)
                    <li>
                        <a href="{{ $item['url'] }}" class="flex items-center gap-2">
                            <span class="text-[0.75rem] font-medium">{{ $item['label'] }}</span>
                            <i class="fas {{ $item['icon'] }} text-[1.5rem]"></i>
                        </a>
                    </li>
                @endforeach
            </ul>
        </nav>
    </x-container>

    <div class="py-6 @if (abbl_on_pro_section()) bg-navy-blue @else bg-neon-green @endif">
        <x-container class="flex justify-between items-center gap-7.5 lg:gap-0 mx-auto">
            @php
            $menu_name = abbl_on_pro_section() ? 'professionals_primary' : 'consumers_primary';
            $menu_class = abbl_on_pro_section() ? 'primary-menu-professionals' : 'primary-menu-consumers';

            $primary_menu = wp_nav_menu([
                'theme_location' => $menu_name,
                'menu_id' => 'primary-menu',
                'menu_class' => $menu_class,
                'container' => 'nav',
                'fallback_cb' => false,
                'echo' => false,
            ]);
            @endphp

            <a class="lg:l-6" href="{{ get_home_url() }}">
                <img class="w-[6.5625rem]" src="{{ Vite::asset(abbl_on_pro_section() ? 'resources/images/abbl-logo.svg' : 'resources/images/abbl-logo-blue.svg') }}" alt="{{ __('ABBL Logo', 'abbl') }}">
            </a>

            <nav class="ml-auto lg:hidden text-white">
                <ul class="flex gap-7.5 text-[1.5rem]">
                    <li>
                        <a href="#">
                            <i class="fas fa-user"></i>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i class="fas fa-search"></i>
                        </a>
                    </li>
                </ul>
            </nav>

            <button id="primary-menu-button" aria-label="{{ __('Toggle navigation', 'abbl') }}" class="flex lg:hidden text-[2.25rem] text-white transition-colors">
                <i class="far fa-bars"></i>
            </button>

            <div id="primary-menu-wrapper" class="z-20 fixed lg:static top-(--header-height) right-0 bottom-0 left-0  w-screen h-[calc(100vh - var(--header-height))] -translate-x-full lg:translate-x-0 bg-navy-blue transition-transform lg:transition-none">
                <x-container class="lg:hidden">
                    <a href="#" class="flex items-center gap-4 pt-6 mb-6 font-bold text-white">
                        <i class="fas fa-arrow-right-arrow-left text-neon-green"></i>
                        <span>
                            @if(abbl_on_pro_section())
                                {{ __('Switch to consumers', 'abbl') }}
                            @else
                                {{ __('Switch to professionals', 'abbl') }}
                            @endif
                        </span>
                    </a>
                    <form action="{{ abbl_search_page_url() }}" class="relative mb-10">
                        <input
                            type="text"
                            placeholder="{{ __('Search', 'abbl') }}"
                            name="search"
                            class="w-full h-12 py-3 px-4  rounded-full text-[0.75rem] bg-white"
                        >
                        <i class="fas fa-search z-10 absolute top-1/2 -translate-y-1/2 right-4 text-[1.5rem] text-navy-blue"></i>
                    </form>
                </x-container>
                <x-container id="sub-menu-infos" class="hidden lg:!hidden pb-8">
                    <nav class="flex justify-between items-center">
                        <button id="sub-menu-infos-close" aria-label="{{ __('Close intermediate navigation', 'abbl') }}" class="flex items-center gap-2 text-neon-green">
                            <i class="far fa-angles-left text-[1.5rem]"></i>
                            <span class="text-[0.625rem]">{{ __('Back', 'abbl') }}</span>
                        </button>
                        <p id="sub-menu-infos-labels" class="text-[0.625rem] text-white [&>*]:last:text-neon-green">
                            {{--
                            <!-- Example of content: -->
                            <span>Home / </span>
                            <span class="text-neon-green">Banking services in Luxembourg</span>
                            --}}
                        </p>
                    </nav>
                </x-container>
                <x-container class="lg:contents">
                    {!! $primary_menu !!}
                </x-container>
                <x-container class="lg:hidden mt-9">
                    <div class="pl-10">
                        <ul class="grid gap-5 pt-9 border-t-2 border-neon-green font-semibold text-white">
                           <li>
                               <a href="#">{{ __('Subscribe to newsletter', 'abbl') }}</a>
                           </li>
                            <li>
                                <a href="#">{{ __('Contact', 'abbl') }}</a>
                            </li>
                            <li>
                                <a href="#">{{ __('Follow us', 'abbl') }}</a>
                            </li>
                        </ul>
                    </div>
                </x-container>
            </div>
        </x-container>
    </div>
</header>
