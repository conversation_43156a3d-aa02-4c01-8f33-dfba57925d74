@import "tailwindcss" theme(static);

@import 'swiper/css';
@import 'swiper/css/pagination';
@import 'swiper/css/navigation';
@import 'swiper/css/a11y';

@source "../views/";
@source "../../app/";

@theme {
  --breakpoint-*: initial;
  --breakpoint-sm: 40rem; /* 640px */
  --breakpoint-md: 64rem; /* 1024px */
  --breakpoint-lg: 75rem; /* 1200px */

  --font-body: 'Poppins', sans-serif;
  --font-headings: 'Funnel Display', sans-serif;

  --text-sm: 0.75rem; /* 12px */

  --color-navy-blue: #223558;
  --color-light-blue: #E0EEF2;
  --color-yellow: #FFBB00;
  --color-neon-green: #7AFFD0;

  --radius-sm: 0.3125rem; /* 5px */
  --radius: 0.625rem; /* 10px */
  --radius-lg: 1.25rem; /* 20px */
}

:root {
  --header-height: 5.8125rem;
}

html {
  font-size: 16px;
  overflow-x: hidden;
}

body {
  max-width: 100vw;
  max-width: 100dvw;
  overflow-x: hidden;
  font-family: var(--font-body), sans-serif;
  line-height: 1.5;
  color: var(--color-navy-blue);
}

a {
  text-decoration: none;
  cursor: pointer;
}

button {
  cursor: pointer;
}

.wysiwyg h2 {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
  font-family: var(--font-headings), sans-serif;
  font-size: theme('fontSize.2xl');
  font-weight: 700;
}

.wysiwyg h2:first-child {
  margin-top: 0;
}

.wysiwyg h2:last-child {
  margin-bottom: 0;
}

.wysiwyg h3 {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
  font-size: theme('fontSize.xl');
  font-weight: 700;
}

.wysiwyg h3:first-child {
  margin-top: 0;
}

.wysiwyg h3:last-child {
  margin-bottom: 0;
}

.wysiwyg p {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
}

.wysiwyg strong {
  font-weight: 500;
}

.wysiwyg p:first-child {
  margin-top: 0;
}

.wysiwyg p:last-child {
  margin-bottom: 0;
}

.wysiwyg a {
  text-decoration: underline;
}

.wysiwyg ul {
  margin-top: theme('spacing.4');
  margin-bottom: theme('spacing.4');
  list-style: disc inside;
}

.wysiwyg ul:first-child {
  margin-top: 0;
}

.wysiwyg ul:last-child {
  margin-bottom: 0;
}

#primary-menu {
  display: flex;
  flex-direction: column;
  gap: theme('spacing.4');
  font-weight: 600;
  color: var(--color-neon-green);
  @variant lg {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 0 theme('spacing.10');
    font-size: 0.75rem;
  }
}

#primary-menu.primary-menu-consumers {
  color: var(--color-navy-blue);
}

#primary-menu > li {
  position: relative;
}

#primary-menu > li > a {
  display: flex;
  align-items: center;
  gap: theme('spacing.3');
  @variant lg {
    display: block;
    gap: 0;
  }
}

#primary-menu > li > a:before {
  content: '\f061';
  display: block;
  width: 0.875rem;
  font-family: "Font Awesome 6 Pro", sans-serif;
  font-weight: 400;
  opacity: 0;
  @variant lg {
    display: none;
  }
}

body.is-primary-menu-active #primary-menu-wrapper {
  translate: 0;
}

body.is-primary-menu-active #primary-menu-button {
  color: var(--color-neon-green);
}

#primary-menu > li.current-menu-ancestor > a:before {
  opacity: 1;
}

#primary-menu > li > ul {
  display: none;
  font-size: 0.875rem;
  font-weight: 400;
  flex-direction: column;
  gap: theme('spacing.5');
  padding-top: 1.25rem;
  padding-left: 2.25rem;
  @variant lg {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    font-size: 1rem;
    background-color: theme('colors.gray.200');
    padding: theme('spacing.3') theme('spacing.4');
  }
}

#primary-menu > li > ul.is-open {
  display: flex;
}

#primary-menu > li:hover > ul {
  @variant lg {
    display: flex;
    flex-direction: column;
    gap: theme('spacing.1');
  }
}

#primary-menu > li > ul > li > a {
  display: grid;
  align-items: center;
  grid-template-columns: 1rem 1rem 1fr;
  gap: 1.25rem;
}

/** Arrow that indicates the current page */
#primary-menu > li > ul > li > a:before {
  content: '';
  font-family: "Font Awesome 6 Pro", sans-serif;
  font-size: 1rem;
  font-weight: 400;
  justify-self: center;
}

/** Context icon */
#primary-menu > li > ul > li > a > i {
  justify-self: center;
}

#primary-menu > li > ul > li.current-menu-item > a:before {
  content: '\f061';
}

#footer-legal-menu {
  display: flex;
  flex-wrap: wrap;
  gap: theme('spacing.8');
  @variant lg {
    gap: theme('spacing.20');
  }
}

#footer-socials-menu {
  display: flex;
  gap: theme('spacing.4');
  font-size: 1.375rem; /* 22px */
  color: theme('color.neon-green');
}

#footer-quick-access-menu {
  list-style: disc inside;
  font-size: 0.875rem;
}

.member-category-button {
  padding: theme('spacing.2') theme('spacing.12');
  border: 1px solid theme('colors.gray.300');
  border-right-width: 0;
  background-color: theme('colors.gray.200');
  cursor: pointer;
}

li:last-child .member-category-button {
  border-right-width: 1px;
}

.member-category-button.active {
  border-bottom-color: var(--color-white);
  font-weight: bold;
  background-color: transparent;
}

.bg-abbl-gradient {
  background-image: linear-gradient(to right, #3269CD, #37BECD);
}

.bg-abbl-gradient-revert {
  background-image: linear-gradient(to left, #3269CD, #37BECD);
}

.bg-abbl-gradient-grain {
  position: relative;
}

.bg-abbl-gradient-grain > * {
  position: relative;
  z-index: 1;
}

.bg-abbl-gradient-grain:before,
.bg-abbl-gradient-grain:after {
  content: '';
  z-index: 0;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.bg-abbl-gradient-grain:before {
  background-image: url('../images/decorations/backgrounds/mesh-gradient.svg');
  background-size: cover;
  background-position: center;
}

.bg-abbl-gradient-grain:after {
  background-image: url('../images/decorations/backgrounds/noise.png');
  opacity: 20%;
}


.glow-neon-green {
  filter: drop-shadow(0 0 3.125rem var(--color-neon-green));
}

.swiper-container {
  position: relative;
}

.swiper-container.with-fade .swiper {
  overflow: visible;
}

.swiper-container .swiper-pagination {
  position: static;
  padding-top: 1.25rem;
  display: flex;
  justify-content: center;
}

.swiper-container .swiper-slide {
  height: auto;
}

.swiper-container .swiper-pagination-bullet {
  display: block;
  opacity: 1;
  width: 10px;
  height: 10px;
  border: 2px solid var(--color-white);
  background-color: transparent;
}

.swiper-container .swiper-pagination-bullet-active {
  border-color: var(--color-yellow);
  background-color: var(--color-yellow);
}

.swiper-container .swiper-button-prev,
.swiper-container .swiper-button-next {
  font-size: 2rem;
}

.swiper-container .swiper-button-prev:after,
.swiper-container .swiper-button-next:after {
  content: none;
}

.swiper-container .swiper-button-prev,
.swiper-container .swiper-button-next {
  top: calc(50% - 15px);
}

.swiper-container .swiper-button-prev {
  left: -4.25rem;
}

.swiper-container .swiper-button-next {
  right: -4.25rem;
}

.swiper-container.with-fade .swiper-slide {
  transition: opacity .3s ease;
  user-select: none;
}

.swiper-container.with-fade .swiper-slide:not(.active) {
  opacity: 0;
  pointer-events: none;
}

#home-hot-topics-slider .swiper-wrapper {
  align-items: center;
}

.archive-filters-contents {
  transition: max-height .3s ease;
}

.archive-filters.archive-filters--open .archive-filters-contents {
  max-height: 400px !important;
}
