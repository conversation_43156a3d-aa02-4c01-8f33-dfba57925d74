<?php

namespace App\Modules;

class TeamMembersModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        register_extended_post_type(CPT_TEAM_MEMBER, [
            'menu_position' => 12,
            'has_archive' => true,
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-groups',
            'supports' => ['title'],
            'taxonomies' => [],
        ], [
            'singular' => 'Team member',
            'plural' => 'Team members',
            'slug' => 'team-members',
        ]);

        register_extended_taxonomy(TAX_TEAM_MEMBER_TEAM, CPT_TEAM_MEMBER, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Teams',
            'singular' => 'Team',
        ]);
    }
}