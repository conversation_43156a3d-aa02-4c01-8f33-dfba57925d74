<?php

namespace App\Fields\PostTypes;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Publication extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('publication');

        $fields
            ->setLocation('post_type', '==', CPT_PUBLICATION);

        $fields
            ->addTrueFalse('is_public')
            ->addUrl('publication_url', [
                'label' => __('Publication URL', 'abbl-backend'),
                'instructions' => __('The URL to the publication in the DMS', 'abbl-backend'),
            ])
            ->addPostObject('published_by', [
                'label' => __('Published by', 'abbl-backend'),
                'post_type' => [CPT_PERSON, CPT_COMPANY],
            ]);

        return $fields->build();
    }
}
