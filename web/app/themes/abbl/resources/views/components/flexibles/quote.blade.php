<blockquote class="grid grid-cols-[19rem_1fr] gap-6.5 w-full mx-auto rounded-lg bg-neon-green">
    <div class="relative">
        @if($image = get_sub_field('quote_author_image'))
            <img class="absolute bottom-0 left-0 right-0 w-[13.25rem] mx-auto" src="{{ $image['url'] }}" alt="{{ $image['alt'] }}">
            <img class="absolute bottom-0 left-0 right-0 w-[19rem] mx-auto" src="{{ Vite::asset('resources/images/decorations/quote-author.svg') }}" alt="">
        @endif
    </div>
    <div class="py-6">
        <div class="mb-6 font-headings text-[1.875rem] font-light text-balance">
            {!! get_sub_field('quote_content') !!}
        </div>
        <cite class="space-y-3 not-italic">
            <p class="block mb-0 leading-[1] text-[1.5rem] font-bold">{{ get_sub_field('quote_author_name') }}</p>
            @if($job = get_sub_field('quote_author_job'))
                <p class="block">{{ $job }}</p>
            @endif
        </cite>
    </div>
</blockquote>
