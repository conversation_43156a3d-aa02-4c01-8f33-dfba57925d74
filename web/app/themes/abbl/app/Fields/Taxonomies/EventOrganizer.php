<?php

namespace App\Fields\Taxonomies;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class EventOrganizer extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('event_organizer');

        $fields
            ->setLocation('taxonomy', '==', TAX_EVENT_ORGANIZER);

        $fields
            ->addImage('logo', [
                'label' => __('Logo', 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
