<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class RelatedNews extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('related_news');

        $fields
            ->addText('related_news_title', [
                'label' => __('Title', 'abbl-backend'),
            ])
            ->addPostObject('related_news_news', [
                'label' => __('News', 'abbl-backend'),
                'post_type' => CPT_POST,
                'multiple' => true,
            ])
            ->addTrueFalse('related_news_show_link_to_archive', [
                'label' => __('Show link to archive', 'abbl-backend'),
                'default_value' => true,
                'instructions' => __('Show a link to the news list page.', 'abbl-backend'),
            ]);

        return $fields;
    }
}
