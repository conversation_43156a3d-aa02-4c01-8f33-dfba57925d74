<?php

namespace App\Fields\Templates;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class FlexiblePage extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('flexible_page');

        $fields
            ->setLocation('page_template', '==', 'templates/template-flexible-page.blade.php');

        $fields
            ->addFlexibleContent('sections')
            ->endFlexibleContent();

        return $fields->build();
    }
}
