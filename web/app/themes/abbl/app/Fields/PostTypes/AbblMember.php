<?php

namespace App\Fields\PostTypes;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class AbblMember extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('abbl_member');

        $fields
            ->setLocation('post_type', '==', CPT_ABBL_MEMBER);

        $fields
            ->addRepeater('items')
                ->addText('item')
            ->endRepeater();

        return $fields->build();
    }
}
