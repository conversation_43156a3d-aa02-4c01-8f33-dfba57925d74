{{--
    Template Name: Contact pro
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', get_the_title()]" />
            </x-container>
        </x-section>

        <x-section>
            <x-container class="grid grid-cols-[3fr_2fr] gap-24">
                <div>
                    <div class="mb-12">
                        <p>Do you need guidance from ABBL experts? Feel free to contact us and we will be sure to get back to you as soon as possible. Exclusively for ABBL members.</p>
                    </div>
                    <form action="" class="space-y-4 w-full">
                        <input class="block w-full px-4 py-2 bg-gray-300" type="text" placeholder="Name">
                        <input class="block w-full px-4 py-2 bg-gray-300" type="text" placeholder="First name">
                        <input class="block w-full px-4 py-2 bg-gray-300" type="email" placeholder="Email address">
                        <input class="block w-full px-4 py-2 bg-gray-300" type="text" placeholder="Organisation">
                        <input class="block w-full px-4 py-2 bg-gray-300" type="text" placeholder="Function">
                        <input class="block w-full px-4 py-2 bg-gray-300" type="text" placeholder="Telephone number">
                        <div class="mt-12">
                            <button class="inline-block px-4 py-2 bg-gray-400" type="submit">Send</button>
                        </div>
                    </form>
                </div>
                <div class="grid place-items-center bg-gray-200">
                    <p>Map ?</p>
                </div>
            </x-container>
        </x-section>
    @endwhile
@endsection
