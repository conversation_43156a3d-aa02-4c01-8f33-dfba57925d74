@php use App\Modules\AbblMembersModule; @endphp
{{--
    Template Name: Members index
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
        the_post();

        $posts_per_page = 28;
        @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar
                aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
            <div class="mt-8">
                <x-link-button href="#">{{ __('Become a member', 'abbl') }}</x-link-button>
            </div>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', 'Hot topics']"/>
            </x-container>
        </x-section>

        <x-section>
            <x-container>
                @php
                    $member_categories = get_terms(['taxonomy' => TAX_ABBL_MEMBER_CATEGORY, 'hide_empty' => true]);
                @endphp

                <ul class="flex">
                    @foreach ($member_categories as $member_category)
                        <li>
                            <button
                                class="member-category-button @if($loop->first) active @endif"
                                data-category="{{ $member_category->slug }}"
                            >
                                {{ $member_category->name }}
                            </button>
                        </li>
                    @endforeach
                </ul>

                @foreach($member_categories as $member_category)
                    @php
                        $members_query = AbblMembersModule::query([
                            'posts_per_page' => $posts_per_page,
                            'tax_query' => [
                                [
                                    'taxonomy' => TAX_ABBL_MEMBER_CATEGORY,
                                    'field' => 'slug',
                                    'terms' => $member_category->slug,
                                ],
                            ],
                        ]);
                    @endphp

                    <div
                        class="member-category-container pt-8 px-4 pb-12 -mt-px border border-gray-300 @unless($loop->first) !hidden @endif"
                        data-category="{{ $member_category->slug }}"
                    >
                        <div class="member-category-vignettes grid grid-cols-4 gap-4" data-category="{{ $member_category->slug }}">
                            @while($members_query->have_posts())
                                @php $members_query->the_post(); @endphp

                                <x-abbl-member-vignette />
                            @endwhile
                        </div>
                        <div class="flex justify-center mt-16 @if($members_query->max_num_pages <= 1) !hidden @endif">
                            <x-link-button
                                class="member-category-load-more"
                                data-category="{{ $member_category->slug }}"
                            >
                                {{ __('Load more results', 'abbl') }}
                            </x-link-button>
                        </div>
                    </div>
                @endforeach
            </x-container>
        </x-section>

        <x-section>
            <x-container class="grid gap-4 text-center">
                <h2 class="text-xl font-bold">
                    Want to join Luxembourg’s financial ecosystem? <br>
                    Find out more about ABBL membership.
                </h2>
                <p class="max-w-[700px] mx-auto">By becoming a member of the ABBL, you join a dynamic community of banking and financial professionals committed to shaping the future of Luxembourg’s financial centre. Members benefit from exclusive insights, networking opportunities, policy updates, and direct access to the ABBL’s expertise and working groups.</p>
                <div class="flex justify-center">
                    <x-link-button>Become a member</x-link-button>
                </div>
            </x-container>
        </x-section>
    @endwhile
@endsection

@push('footer-scripts')
    <script>
        const memberCategoriesButtons = Array.from(document.querySelectorAll('.member-category-button'));
        const memberCategoryContainers = Array.from(document.querySelectorAll('.member-category-container'));
        const postsPerPage = @js($posts_per_page);

        memberCategoriesButtons.forEach((el) => {
            el.addEventListener('click', () => {
                const category = el.dataset.category;

                memberCategoriesButtons.forEach((el) => el.classList.remove('active'));
                el.classList.add('active');

                memberCategoryContainers.forEach((el) => {
                    el.classList.add('!hidden');

                    if (el.dataset.category === category) {
                        el.classList.remove('!hidden');
                    }
                });
            });
        });

        memberCategoryContainers.forEach((container) => {
            let currentPage = 1;

            const loadMoreButton = container.querySelector('.member-category-load-more');

            loadMoreButton.addEventListener('click', async () => {
                currentPage++;

                const url = '/wp-json/abbl/v1/abbl-members?' + new URLSearchParams({
                    'per-page': postsPerPage,
                    'page': currentPage,
                    'abbl-member-category': container.dataset.category,
                });

                const res = await fetch(url);
                const html = await res.text();

                container.querySelector('.member-category-vignettes').insertAdjacentHTML('beforeend', html);

                if (res.headers.get('X-Is-Last-Page') === 'true') {
                    loadMoreButton.closest('div').classList.add('!hidden');
                }
            });
        });
    </script>
@endpush
