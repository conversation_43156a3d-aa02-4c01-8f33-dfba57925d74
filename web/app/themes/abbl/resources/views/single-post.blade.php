@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
        the_post();

        $category = get_the_terms(get_the_ID(), 'category')[0] ?? null;

        $has_takeaways = get_field('has_takeaways');
        @endphp

        <x-flexibles.hero
            :badge="$category?->name ?? null"
            :title="get_the_title()"
            :date="get_the_date(ABBL_DATE_FORMAT_HUMAN)"
            :bg-url="get_the_post_thumbnail_url()"
        >
            <p>{{ get_the_excerpt() }}</p>
        </x-flexibles.hero>

        <aside class="bg-abbl-gradient-grain">
            <div class="py-14">
                <x-container>
                    <div class="flex justify-between mb-6 text-white">
                        <x-back-button href="#">
                            {{ __('Back to news', 'abbl') }}
                        </x-back-button>
                        <x-share-page />
                    </div>
                    @if($has_takeaways)
                        <div class="pt-8 pb-10 px-11 bg-navy-blue/50 rounded-lg">
                            <h2 class="mb-4 font-headings text-[1.25rem] font-bold text-neon-green">
                                {{ __('Key takeaways', 'abbl') }}
                            </h2>
                            <div class="text-white wysiwyg">
                                {!! get_field('takeaways_content') !!}
                            </div>
                        </div>
                    @endif
                </x-container>
            </div>
        </aside>

        <x-section>
            <div class="space-y-20">
                @while(have_rows('sections'))
                    @php the_row(); @endphp

                    @if(get_row_layout() == 'text_columns_layout')
                        <x-container>
                            <x-flexibles.text-columns />
                        </x-container>
                    @endif

                    @if(get_row_layout() == 'centered_image_layout')
                        <x-container>
                            <x-flexibles.centered-image />
                        </x-container>
                    @endif

                    @if(get_row_layout() == 'quote_layout')
                        <x-container>
                            <x-flexibles.quote />
                        </x-container>
                    @endif

                    @if(get_row_layout() == 'related_publications_layout')
                        <x-container>
                            <x-flexibles.related-publications />
                        </x-container>
                    @endif

                    @if(get_row_layout() == 'related_news_layout')
                        <x-container>
                            <x-flexibles.related-news />
                        </x-container>
                    @endif

                    @if(get_row_layout() == 'text_and_big_number_layout')
                        <x-container>
                            <x-flexibles.text-and-big-number />
                        </x-container>
                    @endif
                @endwhile

                <div class="mt-20">
                    <x-container>
                        <div class="mb-6 wysiwyg">
                            <h2>Further reading</h2>
                        </div>
                        <div class="px-5 py-4 rounded bg-light-blue [&_a]:font-bold [&_a]:!underline">
                            <p>Read the official Ministry of Finance press release and download the full termsheet: <a href="#">Finance Europe – Luxembourg Ministry of Finance</a></p>
                        </div>
                    </x-container>
                </div>

                <x-container>
                    <x-post-author-info />
                </x-container>

                <x-container>
                    <x-flexibles.newsletter />
                </x-container>
            </div>
        </x-section>

        <aside class="py-38 bg-abbl-gradient-grain">
            <x-container>
                <h2 class="mb-12 font-headings text-[1.75rem] font-bold text-white">
                    {{ __('More on this topic', 'abbl') }}
                </h2>
                <x-news-slider />
                <div class="mt-1.5">
                    <x-link-button href="#">{{ __('See all related articles', 'abbl') }}</x-link-button>
                </div>
            </x-container>
        </aside>


        <div class="my-12">
            <hr>
            <hr>
            <hr>
            <hr>
            <hr>
        </div>

        <x-section class="space-y-12">
            @while(have_rows('sections'))
                @php the_row(); @endphp

                @if(get_row_layout() == 'takeaways_layout')
                    <x-container>
                        <div class="p-4 bg-gray-200">
                            <h2 class="mb-4 text-lg font-bold">{{ get_sub_field('takeaways_title') }}</h2>
                            <div class="wysiwyg">
                                {!! get_sub_field('takeaways_content') !!}
                            </div>
                        </div>
                    </x-container>
                @endif

                @if(get_row_layout() == 'centered_image_layout')
                    <x-container>
                        <x-flexibles.centered-image />
                    </x-container>
                @endif

                @if(get_row_layout() == 'quote_layout')
                    <x-container>
                        <x-flexibles.quote />
                    </x-container>
                @endif

                @if(get_row_layout() == 'related_publications_layout')
                    @php
                    $publications = get_sub_field('related_publications_publications');
                    @endphp

                    <x-container>
                        <div>
                            <h2 class="mb-4 font-bold">{{ get_sub_field('related_publications_title') }}</h2>
                            <ul>
                                @if($publications && count($publications))
                                    @foreach($publications as $publication)
                                        @php
                                        $publication_category = get_the_terms($publication, TAX_PUBLICATION_CATEGORY)[0] ?? null;
                                        $publication_language = get_the_terms($publication, TAX_PUBLICATION_LANGUAGE)[0] ?? null;
                                        @endphp

                                        <li>
                                            <article class="flex items-center gap-4 p-4 @if($loop->even) bg-gray-100 @else bg-gray-200 @endif" >
                                                <div class="mr-auto">
                                                    @if($publication_category)
                                                        <div class="mb-4">
                                                            <span class="px-2 py-1 text-sm font-bold uppercase bg-gray-300">{{ $publication_category->name }}</span>
                                                        </div>
                                                    @endif
                                                    <div class="flex items-center gap-4">
                                                        @if($publication_language)
                                                            <span class="px-1 py-0.5 text-xs font-bold bg-gray-400">{{ get_field('code', $publication_language) }}</span>
                                                        @endif
                                                        <h3 class="text-xl font-bold">{{ get_the_title($publication) }}</h3>
                                                    </div>
                                                </div>
                                                <div>
                                                    <x-link-button :href="get_the_permalink($publication)">{{ __('See more') }}</x-link-button>
                                                </div>
                                            </article>
                                       </li>
                                    @endforeach
                                @endif
                            </ul>

                            @if(get_sub_field('related_publications_show_link_to_archive'))
                                <div class="mt-2">
                                    <a href="#" class="block text-sm underline">&gt; {{ __('See all publications') }}</a>
                                </div>
                            @endif
                        </div>
                    </x-container>
                @endif

                @if(get_row_layout() == 'related_news_layout')
                    <x-container>
                        <div>
                            <h2 class="mb-4 font-bold">{{ get_sub_field('related_news_title') }}</h2>
                            <ul>
                                @php
                                $news = get_sub_field('related_news_news');
                                @endphp

                                @if($news && count($news))
                                    @foreach($news as $news_item)
                                        @php
                                        $news_category = get_the_terms($news_item, 'category')[0] ?? null;
                                        @endphp

                                        <li>
                                            <a href="{{ get_the_permalink($news) }}"  class="grid grid-cols-[164px_1fr] gap-4 p-4 @if($loop->even) bg-gray-100 @else bg-gray-200 @endif" >
                                                <div>
                                                    @if($thumbnail_url = get_the_post_thumbnail_url($news))
                                                        <img src="{{ $thumbnail_url }}" alt="{{ get_the_post_thumbnail_caption($news) }}">
                                                    @else
                                                        <div class="aspect-[2/1] bg-gray-400">&nbsp;</div>
                                                    @endif
                                                </div>
                                                <div>
                                                    <div class="flex justify-between items-center mb-2">
                                                        <span class="text-xs font-bold">{{ get_the_date(ABBL_DATE_FORMAT_HUMAN, $news_item) }}</span>
                                                        @if($news_category)
                                                            <span class="px-2 py-1 text-xs font-bold uppercase bg-gray-300">{{ $news_category->name }}</span>
                                                       @endif
                                                    </div>
                                                    <div class="flex items-center gap-4">
                                                        <h3 class="text-lg font-bold">{{ get_the_title($news_item) }}</h3>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    @endforeach
                                @endif
                            </ul>

                            @if(get_sub_field('related_news_show_link_to_archive'))
                                <div class="mt-2">
                                    <a href="#" class="block text-sm underline">&gt; {{ __('See all news') }}</a>
                                </div>
                            @endif
                        </div>
                    </x-container>
                @endif

                @if(get_row_layout() === 'big_number_layout')
                    <x-container>
                        <div class="max-w-[746px] px-36 py-10 mx-auto text-center bg-gray-200">
                            <p class="mb-4 text-4xl font-bold opacity-50"><strong>€1,000 BILLION</strong></p>
                            <p>New savings generated every year by European citizens across the EU — a major lever for green and digital transitions.</p>
                        </div>
                    </x-container>
                @endif
            @endwhile
        </x-section>

        <x-section>
            <x-container>
                <aside class="grid grid-cols-[149px_1fr] items-center p-6 gap-8 bg-gray-200">
                    @php
                    $author_id = get_the_author_meta('ID');
                    @endphp

                    <div class="aspect-square rounded-full bg-gray-400"></div>
                    <div class="space-y-1">
                        <h3 class="text-xl font-bold">{{ get_the_author() }}</h3>
                        @if($job = get_field('author_job', 'user_'.$author_id))
                            <p>{{ $job }}</p>
                        @endif
                        <p class="font-bold">Published on {{ get_the_date(ABBL_DATE_FORMAT_HUMAN) }}</p>
                        @if($linkedin_profile = get_field('author_linkedin_profile', 'user_'.$author_id))
                            <div class="my-4">
                                <a href="{{ $linkedin_profile }}" target="_blank" class="inline-flex items-center gap-4 px-2 text-black-800 bg-gray-400">
                                    <i class="fab fa-linkedin-in text-sm"></i> LinkedIn Profile
                                </a>
                            </div>
                        @endif
                        <p class="text-sm underline">
                            <a href="#">See all articles by {{ get_the_author() }}</a>
                        </p>
                    </div>
                </aside>
            </x-container>
        </x-section>

        <x-section>
            <x-flexibles.newsletter />
        </x-section>

        <x-section class="bg-gray-50 py-12 my-0">
            <x-container>
                <h2 class="mb-8 text-xl font-bold">{{ __('More on this topic', 'abbl') }}</h2>
                <div class="grid grid-cols-3 gap-4 mb-8">
                    @php
                        $news_query = new WP_Query([
                            'post_type' => 'post',
                            'posts_per_page' => 3,
                            'not__in' => [get_the_ID()],
                        ]);
                    @endphp

                    @while($news_query->have_posts())
                        @php $news_query->the_post(); @endphp

                        <x-news-card />
                    @endwhile
                </div>
                <div class="flex justify-center">
                    <x-link-button href="#">{{ __('See all related articles', 'abbl') }}</x-link-button>
                </div>
            </x-container>
        </x-section>
    @endwhile
@endsection
