@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php
            the_post();

            $featured_image_url = get_the_post_thumbnail_url();

            $category = get_the_terms(get_the_ID(), TAX_EVENT_CATEGORY)[0] ?? null;
            $organizer = get_the_terms(get_the_ID(), TAX_EVENT_ORGANIZER)[0] ?? null;
            $venue = get_the_terms(get_the_ID(), TAX_EVENT_VENUE)[0] ?? null;
            $languages = get_the_terms(get_the_ID(), TAX_EVENT_LANGUAGE) ?: [];
            $formats = get_the_terms(get_the_ID(), TAX_EVENT_FORMAT) ?: [];

            $partners = [];
        @endphp

        <x-flexibles.hero :title="get_the_title()" :badge="$category->name ?? null" :bg-url="$featured_image_url">
            {!! get_the_excerpt() !!}
        </x-flexibles.hero>

        <aside class="pt-20 pb-7 bg-abbl-gradient">
            <x-container>
                {{-- TODO: Extract this maybe ? --}}
                <div class="flex justify-between mb-6 text-white">
                    <a href="#" class="flex items-center gap-4 text-[0.9375rem] font-bold uppercase">
                        <i class="fas fa-arrow-left"></i> {{ __('Back to news', 'abbl') }}
                    </a>
                    <button>
                        <i class="fas fa-share-nodes"></i>
                    </button>
                </div>
                {{-- ENDTODO --}}
            </x-container>
        </aside>

        <section class="relative">
            <div class="absolute top-0 right-0 left-0 h-[15rem] bg-abbl-gradient"></div>

            <x-container class="relative">
                <div class="grid grid-cols-[2fr_1fr] gap-12 mb-7">
                    @if($featured_image_url = get_the_post_thumbnail_url())
                        <div class="overflow-hidden rounded-lg">
                            <img class="w-full h-auto" src="{{ $featured_image_url }}" alt="{{ get_the_post_thumbnail_caption() }}">
                        </div>
                    @endif

                    <div class="flex flex-col p-6 rounded-lg bg-neon-green">
                        <div class="mb-7.5">
                            <x-category-badge :category="$category"/>
                        </div>

                        <div class="grid gap-2 mb-7.5">
                            @php
                                $start_date = abbl_acf_to_carbon(get_field('start_datetime'));
                                $end_date = abbl_acf_to_carbon(get_field('end_datetime'));
                            @endphp

                            @if ($start_date)
                                <p class="flex items-center gap-4">
                                    <i class="relative bottom-0.5 fas fa-calendar-days"></i>
                                    <span class="font-bold">{{ $start_date->format('d F Y') }}</span>
                                </p>

                                <p class="flex items-center gap-4">
                                    <i class="far fa-clock"></i>
                                    <span>{{ $start_date->format('H:i') }} @if($end_date) - {{ $end_date->format('H:i') }} @endif</span>
                                </p>
                            @endif

                            @if($venue && $address = get_field('address', $venue))
                                <div class="flex gap-4">
                                    <i class="relative top-1 far fa-location-dot"></i>
                                    <span>{!! $address !!}</span>
                                </div>
                            @endif
                        </div>

                        <div class="space-y-2.5 mb-6 text-[0.875rem]">
                            @if(count($languages))
                                <div class="flex items-center gap-3 mt-4">
                                    <span class="font-bold">Language(s):</span>
                                    @foreach($languages as $language)
                                        <div class="flex gap-3">
                                            <span class="inline-block py-1 px-3 mr-2 rounded-sm text-[0.75rem] font-bold uppercase bg-white">
                                                {{ $language->name }}
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            @if(count($formats))
                                <p>
                                    <span class="font-bold">Format:</span>
                                    @foreach($formats as $format)
                                        <span>{{ $format->name }}@if(!$loop->last), @endif</span>
                                    @endforeach
                                </p>
                            @endif

                            <p>
                                <span class="font-bold">Audience:</span>
                                <span>Reserved for Members</span>
                            </p>
                        </div>

                        <div class="mt-auto">
                            <x-link-button class="w-full">Register now</x-link-button>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-[2fr_1fr] gap-12">
                    <div>
                        <div class="flex items-center gap-12 px-7 py-3.5 rounded bg-yellow">
                            <i class="text-[1.5rem] far fa-info-circle"></i>
                            <div class="text-[0.875rem] text-black">
                                <p class="font-bold">The event is open for registrations.</p>
                                <p>Open to all ABBL members – no personal invitation required.</p>
                            </div>
                        </div>

                        <div class="pr-[6.315rem]">
                            <div class="wysiwyg mt-12 text-[1.125rem]">
                                @php the_content() @endphp
                            </div>

                            @if($venue && $coordinates = get_field('coordinates', $venue))
                                <aside class="mt-12">
                                    <div class="wysiwyg mb-5">
                                        <h2>{{ __('Event location', 'abbl') }}</h2>
                                    </div>
                                    <div
                                        id="venue-gmap"
                                        class="aspect-[2/1] grid place-items-center p-4 rounded-lg"
                                        data-lat="{{ $coordinates['lat'] }}"
                                        data-lng="{{ $coordinates['lng'] }}"
                                        data-zoom="{{ $coordinates['zoom'] ?? 16 }}"
                                    ></div>
                                </aside>
                            @endif
                        </div>
                    </div>
                    <div class="space-y-6">
                        @php
                            $disclaimer = get_field('disclaimer') ?: get_field('events_default_disclaimer', 'options');
                        @endphp

                        @if($disclaimer)
                            <div class="text-[0.625rem] mb-6 text-[#868686]">
                                {!! $disclaimer !!}
                            </div>
                        @endif

                        <x-sidebar-card>
                            <div class="flex justify-between items-center gap-4">
                                <div>
                                    <p>Event starts in:</p>
                                    <p class="text-[1.25rem] font-semibold">03d 12h 26min</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-[1.25rem] font-bold">18</p>
                                    <p>seats left</p>
                                </div>
                            </div>
                        </x-sidebar-card>

                        <x-sidebar-card>
                            <div class="flex items-center gap-7">
                                <i class="text-[3rem] fab fa-linkedin-in"></i>
                                <p class="text-[0.875rem] font-medium">Follow us on LinkedIn for <br class="hidden lg:block"> event photos, recaps and insights</p>
                            </div>
                        </x-sidebar-card>

                        @if($organizer)
                            @php
                                $logo = get_field('logo', $organizer);
                            @endphp

                            <x-sidebar-card>
                                <div class="grid @if($logo) grid-cols-[auto_1fr] @endif gap-8">
                                    <div>
                                        <p>Organized by :</p>
                                        <p class="text-[1.25rem] font-bold">{{ $organizer->name }}</p>
                                    </div>
                                    @if($logo)
                                        <div class="flex justify-center items-center rounded bg-white">
                                            <img src="{{ $logo['url'] }}" alt="{{ $logo['alt'] }}">
                                        </div>
                                    @endif
                                </div>
                            </x-sidebar-card>
                        @endif

                        <x-sidebar-card>
                            <h2 class="mb-6 text-[1.25rem] font-bold">{{ __('In partnership with', 'abbl') }}</h2>
                            <ul class="grid grid-cols-3 justify-center gap-4">
                                @php
                                $hardcoded_partners = [
                                    ['title' => 'BGL', 'image' => Vite::asset('resources/images/needs-integration/partnership-bgl.png')],
                                    ['title' => 'BIL', 'image' => Vite::asset('resources/images/needs-integration/partnership-bil.png')],
                                    ['title' => 'BDL', 'image' => Vite::asset('resources/images/needs-integration/partnership-bdl.png')],
                                ];
                                @endphp

                                @foreach ($hardcoded_partners as $partner)
                                    <li class="text-center">
                                        <img class="w-[3.625rem] mx-auto mb-5" src="{{ $partner['image'] }}" alt="">
                                        <p class="font-bold">{{ $partner['title'] }}</p>
                                    </li>
                                @endforeach
                            </ul>
                        </x-sidebar-card>

                        @if(have_rows('speakers'))
                            <x-sidebar-card>
                                <h2 class="mb-6 text-[1.25rem] font-bold">{{ __('Speakers', 'abbl') }}</h2>
                                <ul id="event-speakers" class="grid gap-4">
                                    @php $i = 0; @endphp

                                    @while(have_rows('speakers'))
                                        @php
                                            the_row();

                                            $speaker = get_sub_field('speaker');
                                            $role = get_sub_field('role');
                                            $linkedin_url = get_field('linkedin_url', $speaker);
                                        @endphp

                                        @if($speaker)
                                            <li class="flex items-center gap-4 @if($i >= 5) hidden @endif">
                                                @if ($portrait_url = get_the_post_thumbnail_url($speaker))
                                                    <img class="aspect-square w-[3.625rem] rounded-full" src="{{ $portrait_url }}"
                                                         alt="{{ get_the_post_thumbnail_caption($speaker) }}">
                                                @endif
                                                <div>
                                                    <div class="flex gap-2">
                                                        <p class="text-lg font-bold">{{ get_the_title($speaker) }}</p>
                                                        @if($role || $linkedin_url)
                                                            <div class="flex items-center gap-2">
                                                                @if($role)
                                                                    <span class="inline-flex items-center h-[0.8125rem] px-2 pt-0.25 rounded-sm text-[0.5rem] font-bold bg-white">
                                                                        {{ $role }}
                                                                    </span>
                                                                @endif
                                                                @if($linkedin_url)
                                                                    <a href="{{ $linkedin_url }}" target="_blank">
                                                                        <i class="fab fa-linkedin"></i>
                                                                    </a>
                                                                @endif
                                                            </div>
                                                        @endif
                                                    </div>
                                                    @if ($job_title = get_field('job_title', $speaker))
                                                        <p class="text-[0.75rem]">{{ $job_title }}</p>
                                                    @endif
                                                </div>
                                            </li>
                                        @endif

                                        @php $i++; @endphp
                                    @endwhile
                                </ul>
                                @if($i >= 5)
                                    <div class="flex justify-center mt-8">
                                        <button id="see-all-speakers" class="flex justify-between items-center w-full px-5 pr-16 py-3 rounded font-semibold bg-[#6BF0C2] hover:cursor-pointer">
                                            <i class="text-[1.5rem] far fa-podium"></i> See all speakers
                                        </button>
                                    </div>
                                @endif
                            </x-sidebar-card>
                        @endif

                        <x-sidebar-card>
                            <h2 class="mb-6 text-[1.25rem] font-bold">{{ __('Contact the organiser', 'abbl') }}</h2>
                            <div class="flex items-center gap-4 mb-5">
                                @if ($portrait_url = get_the_post_thumbnail_url($speaker))
                                    <img class="aspect-square w-[3.625rem] rounded-full" src="{{ $portrait_url }}"
                                         alt="{{ get_the_post_thumbnail_caption($speaker) }}">
                                @endif
                                <div>
                                    <div class="flex gap-2">
                                        <p class="text-lg font-bold">John Travolta</p>
                                        <div class="flex items-center gap-2">
                                            <span class="inline-flex items-center h-[0.8125rem] px-2 pt-0.25 rounded-sm text-[0.5rem] font-bold bg-white">
                                                Keynote speaker
                                            </span>
                                            <a href="#" target="_blank">
                                                <i class="fab fa-linkedin"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <p class="text-[0.75rem]">ABBL - head of business communication</p>
                                </div>
                            </div>
                            <div class="space-y-3.5">
                                <a href="#" class="flex items-center gap-2">
                                    <i class="w-[3.625rem] text-center far fa-paper-plane"></i>
                                    <span class="text-[0.875rem]"><EMAIL></span>
                                </a>
                                <a href="#" class="flex items-center gap-2">
                                    <i class="w-[3.625rem] text-center far fa-phone"></i>
                                    <span class="text-[0.875rem]">+352 44 44 44 44</span>
                                </a>
                            </div>
                        </x-sidebar-card>
                    </div>
                </div>
            </x-container>
        </section>

        <section class="my-36">
            <x-container class="flex flex-col items-center">
                <h2 class="mb-6 font-headings text-[1.5rem] font-bold text-center">Interested in joining this event ?</h2>
                <x-link-button href="#" class="w-[21.25rem]">Register now</x-link-button>
            </x-container>
        </section>

        <section class="py-36 bg-abbl-gradient">
            <x-container>
                <h2 class="mb-12 font-headings text-[1.5rem] font-bold text-light-blue">More on this topic</h2>
                <div>{{-- TODO: slider goes here --}}</div>
                <x-link-button href="#">See all related articles</x-link-button>
            </x-container>
        </section>

        {{-- TODO: Newsletter goes here --}}
    @endwhile
@endsection

@if($venue && $coordinates)
    @push('footer-scripts')
        {!! abbl_google_map_script_tag() !!}

        <script type="text/javascript">
          const venueMapElement = document.querySelector('#venue-gmap');

          if (venueMapElement) {
            const point = {
              lat: parseFloat(venueMapElement.dataset.lat),
              lng: parseFloat(venueMapElement.dataset.lng)
            };

            const map = new google.maps.Map(venueMapElement, {
              center: point,
              zoom: parseInt(venueMapElement.dataset.zoom),
            });

            new google.maps.Marker({
              position: point,
              map,
            });
          }
        </script>
    @endpush
@endif

@push('footer-scripts')
    <script>
      const allSpeakersButton = document.querySelector('#see-all-speakers');
      const speakers = document.querySelectorAll('#event-speakers li');

      if (allSpeakersButton) {
        allSpeakersButton.addEventListener('click', () => {
          speakers.forEach((el) => el.classList.remove('hidden'));
          allSpeakersButton.parentNode.classList.add('hidden');
        });
      }
    </script>
@endpush
