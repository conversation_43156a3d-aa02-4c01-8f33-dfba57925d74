<?php

namespace App\Modules;

class SharedModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
    }

    public static function registerTypes()
    {
        register_extended_taxonomy(TAX_SHARED_SITE_SECTION, [CPT_POST, CPT_PUBLICATION, CPT_EVENT, CPT_HOT_TOPIC], [], [
            'plural' => 'Site sections',
            'singular' => 'Site section',
        ]);
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = CPT_ABBL_MEMBER;
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;

        return new \WP_Query($query);
    }
}
