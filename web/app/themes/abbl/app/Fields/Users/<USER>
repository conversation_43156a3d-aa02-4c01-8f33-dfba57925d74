<?php

namespace App\Fields\Users;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class JobPoster extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('job_poster');

        $fields
            ->setLocation('user_role', '==', 'job_poster');

        $fields
            ->addImage('company_logo', [
                'label' => __('Company logo', 'abbl-backend'),
                'instructions' => __('The logo of the company you work for', 'abbl-backend'),
            ])
            ->addText('company_name', [
                'label' => __('Company name', 'abbl-backend'),
                'instructions' => __('The name of the company you work for', 'abbl-backend'),
            ])
            ->addWysiwyg('company_description', [
                'label' => __('Company description', 'abbl-backend'),
                'instructions' => __('A short description of the company you work for', 'abbl-backend'),
            ])
            ->addUrl('company_website_url', [
                'label' => __('Company website URL', 'abbl-backend'),
                'instructions' => __('The URL of the company website', 'abbl-backend'),
            ])
            ->addUrl('company_linkedin_url', [
                'label' => __('Company Linkedin URL', 'abbl-backend'),
                'instructions' => __("The URL of the company's Linkedin page", 'abbl-backend'),
            ]);

        return $fields->build();
    }
}
