<?php

/**
 * Theme setup.
 */

namespace App;

use App\Modules\AbblMembersModule;
use App\Modules\CompaniesModule;
use App\Modules\EventsModule;
use App\Modules\HotTopicsModule;
use App\Modules\JobOffersModule;
use App\Modules\PeopleModule;
use App\Modules\PostsModule;
use App\Modules\PublicationsModule;
use App\Modules\SharedModule;
use App\Modules\TeamMembersModule;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Vite;

/**
 * Inject styles into the block editor.
 *
 * @return array
 */
add_filter('block_editor_settings_all', function ($settings) {
    $style = Vite::asset('resources/css/editor.css');

    $settings['styles'][] = [
        'css' => "@import url('{$style}')",
    ];

    return $settings;
});

/**
 * Inject scripts into the block editor.
 *
 * @return void
 */
add_filter('admin_head', function () {
    if (! get_current_screen()?->is_block_editor()) {
        return;
    }

    $dependencies = json_decode(Vite::content('editor.deps.json'));

    foreach ($dependencies as $dependency) {
        if (! wp_script_is($dependency)) {
            wp_enqueue_script($dependency);
        }
    }

    echo Vite::withEntryPoints([
        'resources/js/editor.js',
    ])->toHtml();
});

/**
 * Use the generated theme.json file.
 *
 * @return string
 */
add_filter('theme_file_path', function ($path, $file) {
    return $file === 'theme.json'
        ? public_path('build/assets/theme.json')
        : $path;
}, 10, 2);

/**
 * Register the initial theme setup.
 *
 * @return void
 */
add_action('after_setup_theme', function () {
    /**
     * Disable full-site editing support.
     *
     * @link https://wptavern.com/gutenberg-10-5-embeds-pdfs-adds-verse-block-color-options-and-introduces-new-patterns
     */
    remove_theme_support('block-templates');

    /**
     * Register the navigation menus.
     *
     * @link https://developer.wordpress.org/reference/functions/register_nav_menus/
     */
    register_nav_menus([
        'professionals_primary' => __('Professionals primary menu', 'abbl'),
        'consumers_primary' => __('Consumers primary menu', 'abbl'),
        'footer_legal' => __('Footer legal menu', 'abbl'),
        'footer_socials' => __('Footer socials menu', 'abbl'),
        'footer_quick_access' => __('Footer quick access menu', 'abbl'),
    ]);

    /**
     * Disable the default block patterns.
     *
     * @link https://developer.wordpress.org/block-editor/developers/themes/theme-support/#disabling-the-default-block-patterns
     */
    remove_theme_support('core-block-patterns');

    /**
     * Enable plugins to manage the document title.
     *
     * @link https://developer.wordpress.org/reference/functions/add_theme_support/#title-tag
     */
    add_theme_support('title-tag');

    /**
     * Enable post thumbnail support.
     *
     * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
     */
    add_theme_support('post-thumbnails');

    /**
     * Enable responsive embed support.
     *
     * @link https://developer.wordpress.org/block-editor/how-to-guides/themes/theme-support/#responsive-embedded-content
     */
    add_theme_support('responsive-embeds');

    /**
     * Enable HTML5 markup support.
     *
     * @link https://developer.wordpress.org/reference/functions/add_theme_support/#html5
     */
    add_theme_support('html5', [
        'caption',
        'comment-form',
        'comment-list',
        'gallery',
        'search-form',
        'script',
        'style',
    ]);

    /**
     * Enable selective refresh for widgets in customizer.
     *
     * @link https://developer.wordpress.org/reference/functions/add_theme_support/#customize-selective-refresh-widgets
     */
    add_theme_support('customize-selective-refresh-widgets');
}, 20);

/**
 * Register the theme sidebars.
 *
 * @return void
 */
add_action('widgets_init', function () {
    $config = [
        'before_widget' => '<section class="widget %1$s %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h3>',
        'after_title' => '</h3>',
    ];

    register_sidebar([
        'name' => __('Primary', 'abbl'),
        'id' => 'sidebar-primary',
    ] + $config);

    register_sidebar([
        'name' => __('Footer', 'abbl'),
        'id' => 'sidebar-footer',
    ] + $config);
});

add_action('acf/init', function() {
    acf_update_setting('google_api_key', env('GOOGLE_MAPS_KEY'));
});

add_filter('date_formats', function($default_date_formats) {
    $default_date_formats[] = ABBL_DATE_FORMAT;

    return $default_date_formats;
});

add_filter('get_the_excerpt', function ($excerpt) {
    $regex = [
        '|<a[^>]*>[^<]+</a>|',
        '|<a[^>]*><span[^>]*>[^<]+</span></a>|',
    ];

    return preg_replace($regex, '', $excerpt);
});

add_filter('excerpt_length', function() {
    return 20;
});

add_filter('wp_nav_menu_objects', function ($items, $args) {
    if ($args->theme_location !== 'footer_socials') {
        return $items;
    }

    foreach($items as $item) {
        if ($icon = get_field('icon', $item)) {
            $item->title = "<i class='$icon'></i>";
        }
    }

    return $items;
}, 10, 2);

add_filter('wp_nav_menu_objects', function ($items, $args) {
    if (!in_array($args->theme_location, ['professionals_primary', 'consumers_primary'])) {
        return $items;
    }

     foreach($items as $item) {
        $icon = get_field('icon', $item);

        $item->title = "<i class='$icon'></i><span>$item->title</span>";
     }

    return $items;
}, 10, 2);

PostsModule::bootstrap();
PeopleModule::bootstrap();
EventsModule::bootstrap();
HotTopicsModule::bootstrap();
PublicationsModule::bootstrap();
JobOffersModule::bootstrap();
CompaniesModule::bootstrap();
TeamMembersModule::bootstrap();
AbblMembersModule::bootstrap();
SharedModule::bootstrap();

add_action('rest_api_init', function () {
    register_rest_route('abbl/v1', '/abbl-members', [
        'methods' => 'GET',
        'callback' => function (\WP_REST_Request $request) {
            $query = AbblMembersModule::query([
                'posts_per_page' => $request->get_param('per-page'),
                'paged' => $request->get_param('page'),
                'tax_query' => [
                    [
                        'taxonomy' => TAX_ABBL_MEMBER_CATEGORY,
                        'field' => 'slug',
                        'terms' => $request->get_param('abbl-member-category'),
                    ],
                ],
            ]);

            $posts = $query->get_posts();

            $html = collect($posts)
                ->map(fn($p) => view('components.abbl-member-vignette', ['id' => $p->ID])->render())
                ->implode('');

            $is_last_page = $query->max_num_pages === intval($request->get_param('page')) ? 'true' : 'false';

            header('Content-Type: text/html');
            header('X-Is-Last-Page: ' . $is_last_page);

            echo $html;

            exit();
        },
    ]);
});

add_action('pre_get_posts', function ($query) {
    $is_site_filterable_post_type = in_array($query->get('post_type'), [CPT_POST, CPT_PUBLICATION, CPT_EVENT, CPT_HOT_TOPIC]);

    $ignore_filter_key = 'ignore_site_filtering';

    if (!is_admin() && $is_site_filterable_post_type && !$query->is_main_query()) {
        $ignore_filter = $query->get($ignore_filter_key) ?: false;

        if ($ignore_filter) {
            return;
        }

        $tax_query = $query->get('tax_query') ?: [];

        $tax_query[] = [
            'taxonomy' => TAX_SHARED_SITE_SECTION,
            'field' => 'slug',
            'terms' => abbl_on_pro_section() ? 'professionals' : 'consumers',
        ];

        $query->set('tax_query', $tax_query);
    }
});
