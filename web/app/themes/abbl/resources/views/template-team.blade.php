{{--
    Template Name: Team
--}}

@extends('layouts.app')

@section('content')
    @while(have_posts())
        @php the_post(); @endphp

        <x-flexibles.hero :title="get_the_title()">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus non fermentum erat. Maecenas pulvinar aliquet nibh, vitae sodales nunc faucibus eu. Nulla ornare sodales elementum.</p>
        </x-flexibles.hero>

        <x-section>
            <x-container>
                <x-breadcrumbs :segments="['ABBL Pro', get_the_title()]" />
            </x-container>
        </x-section>

        <x-section>
            <x-container>
                <form class="flex gap-4 p-4 bg-gray-300" method="GET">
                    @php
                    $teams = get_terms(['taxonomy' => TAX_TEAM_MEMBER_TEAM, 'hide_empty' => false]);
                    @endphp

                    <select
                        name="team-member-team"
                        class="p-2 bg-gray-200"
                        onchange="this.closest('form').submit();"
                    >
                        <option value="">{{ __('Filter by team', 'abb') }}</option>
                        @foreach ($teams as $team)
                            <option value="{{ $team->slug }}" @if(get_query_var('team-member-team') == $team->slug) selected @endif>{!! $team->name !!}</option>
                        @endforeach
                    </select>

                    <button class="ml-auto text-2xl">
                        <span class="fas fa-recycle"></span>
                    </button>
                </form>
            </x-container>
        </x-section>

        @php
        $teams = get_terms(['taxonomy' => TAX_TEAM_MEMBER_TEAM, 'hide_empty' => true]);
        @endphp

        @foreach ($teams as $team)
            @php
            if (!empty($_GET['team-member-team']) && $_GET['team-member-team'] !== $team->slug) {
                continue;
            }

            $team_members_query = new WP_Query([
                'post_type' => CPT_TEAM_MEMBER,
                'tax_query' => [
                    [
                        'taxonomy' => TAX_TEAM_MEMBER_TEAM,
                        'field' => 'slug',
                        'terms' => $team->slug,
                    ],
                ],
            ]);
            @endphp

            @if($team_members_query->have_posts())
                <x-section>
                    <x-container>
                        <h2 class="text-2xl font-bold mb-4">{!! $team->name !!}</h2>
                        <ul class="grid grid-cols-3 gap-8">
                            @while($team_members_query->have_posts())
                                @php $team_members_query->the_post(); @endphp

                                <li class="p-4 bg-gray-200">
                                    @if($portrait = get_field('portrait'))
                                        <img class="aspect-[3/2] mb-4" src="{{ $portrait['url'] }}" alt="{{ $portrait['alt'] }}">
                                    @endif
                                    <div class="flex flex-col items-center gap-2 mb-4">
                                        <h3 class="text-xl font-bold">{{ get_the_title() }}</h3>
                                        <p class="px-4 py-1 @if(get_field('highlight_job_title')) bg-white @endif">{{ get_field('job_title') }}</p>
                                        @if($linkedin_url = get_field('linkedin_url'))
                                            <a href="{{ $linkedin_url }}" target="_blank" class="text-2xl">
                                                <i class="fab fa-linkedin"></i>
                                            </a>
                                        @endif
                                    </div>
                                    <div class="wysiwyg">
                                        {!! get_field('short_bio') !!}
                                    </div>
                                </li>
                            @endwhile

                            @php wp_reset_postdata(); @endphp
                        </ul>
                    </x-container>
                </x-section>
            @endif
        @endforeach

        @php
        global $wpdb;

        $abbl_jobs_query = $wpdb->prepare("
            SELECT
                $wpdb->posts.ID,
                $wpdb->posts.post_title
            FROM
                $wpdb->posts
            LEFT JOIN
                $wpdb->users ON $wpdb->users.ID = $wpdb->posts.post_author
            WHERE
                post_type = %s
                AND post_status = 'publish'
                # TODO: Filter this correctly, probably using an ACF fields only an administrator can add ?
                AND $wpdb->users.user_email LIKE '%wrobert%';
        ", [CPT_JOB_OFFER]);

        $abbl_jobs = $wpdb->get_results($abbl_jobs_query);
        @endphp

        @if($abbl_jobs && count($abbl_jobs))
            <aside>
                <x-section>
                    <x-container>
                        <div class="max-w-[700px] mx-auto text-center">
                            <h2 class="mb-4 text-xl font-bold">We’re hiring!</h2>
                            <div class="wysiwyg mb-8">
                                <p>Join the ABBL team and help shape the future of finance in Luxembourg. Whether you're just starting your career or looking for a new challenge, explore our current opportunities and discover what it's like to work in a dynamic, forward-thinking organisation.</p>
                            </div>
                            <div class="flex justify-center">
                                {{-- TODO: Link to the jobs page with some query param --}}
                                <x-link-button href="#">See ABBL job offers</x-link-button>
                            </div>
                        </div>
                    </x-container>
                </x-section>
            </aside>
        @endif
    @endwhile
@endsection