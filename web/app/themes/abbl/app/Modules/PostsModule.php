<?php

namespace App\Modules;

class PostsModule
{
    public static function bootstrap()
    {
        // We'll keep this empty for now.
    }

    public static function registerTypes()
    {
        // We'll keep this empty for now.
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = 'post';
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;

        $ignore_featured = $query['ignore_featured'] ?? false;

        if (!$ignore_featured) {
            $query['order'] = array_merge(['is_featured' => 'DESC'], $query['orderby'] ?? []);
        }

        return new \WP_Query($query);
    }
}
