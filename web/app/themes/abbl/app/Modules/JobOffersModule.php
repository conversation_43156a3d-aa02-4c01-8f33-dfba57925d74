<?php

namespace App\Modules;

class JobOffersModule
{
    public static function bootstrap()
    {
        add_action('init', [self::class, 'registerTypes']);
        add_filter('query_vars', [self::class, 'queryVars']);

        add_action('admin_menu', function() {
            if (current_user_can('job_poster')) {
                remove_menu_page('index.php');
            }
        });

        add_action('init', function() {
            global $wp_post_types;

            $author_caps = get_role('subscriber')->capabilities;

            add_role('job_poster', 'Job poster', $author_caps);

            // TODO: Iron this out
            $role = get_role('job_poster');

            $role->add_cap('edit_job_offers');
            $role->add_cap('edit_job_offer');
            $role->add_cap('edit_others_job_offers', false);
            $role->add_cap('delete_others_job_offers', false);
            $role->add_cap('publish_job_offers');
            $role->add_cap('read_job_offers');
            $role->add_cap('read_private_job_offers');
            $role->add_cap('delete_job_offers');

            // Give all capabilities to administrators
            $role = get_role('administrator');
            $role->add_cap('edit_job_offers');
            $role->add_cap('edit_job_offer');
            $role->add_cap('edit_others_job_offers');
            $role->add_cap('delete_others_job_offers');
            $role->add_cap('publish_job_offers');
            $role->add_cap('read_job_offers');
            $role->add_cap('read_private_job_offers');
            $role->add_cap('delete_job_offers');

            $wp_post_types[CPT_JOB_OFFER]->capability_type = 'job_offer';
        });

        add_action('pre_get_posts', function($query) {
            if (is_admin() && is_post_type_archive([CPT_JOB_OFFER]) && current_user_can('job_poster')) {
                $query->set('author', get_current_user_id());
            }
        });
    }

    public static function registerTypes()
    {
        register_extended_post_type(CPT_JOB_OFFER, [
            'menu_position' => 4,
            'has_archive' => false,
            'show_in_rest' => true,
            'menu_icon' => 'dashicons-businessman',
            'supports' => ['title', 'editor', 'thumbnail', 'excerpt'],
            'capabilities' => [
                'edit_post' => 'edit_job_offer',
                'read_post' => 'read_job_offer',
                'delete_post' => 'delete_job_offer',
                'edit_posts' => 'edit_job_offers',
                'edit_others_posts' => 'edit_others_job_offers',
                'delete_posts' => 'delete_job_offers',
                'publish_posts' => 'publish_job_offers',
                'read_private_posts' => 'read_private_job_offers',
            ],
            'admin_filters' => [
                'company' => [
                    'taxonomy' => TAX_JOB_OFFER_COMPANY,
                    'label' => 'Company',
                ],
            ],
        ], [
            'singular' => 'Job offer',
            'plural' => 'Job offers',
            'slug' => 'job-offers',
        ]);

        register_extended_taxonomy(TAX_JOB_OFFER_CONTRACT_TYPE, CPT_JOB_OFFER, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Contract types',
            'singular' => 'Contract type',
        ]);

        register_extended_taxonomy(TAX_JOB_OFFER_FIELD, CPT_JOB_OFFER, [
            'meta_box' => 'radio',
        ], [
            'plural' => 'Fields',
            'singular' => 'Field',
        ]);
    }

    public static function queryVars($query_vars) {
        $query_vars[] = TAX_JOB_OFFER_CONTRACT_TYPE;
        $query_vars[] = TAX_JOB_OFFER_FIELD;
        $query_vars[] = 'job-offer-job-poster';

        return $query_vars;
    }

    public static function query(array $query = []): \WP_Query {
        $query['post_type'] = CPT_JOB_OFFER;
        $query['posts_per_page'] = $query['posts_per_page'] ?? 12;
        $query['tax_query'] = $query['tax_query'] ?? [];

        if ($term = get_query_var(TAX_JOB_OFFER_CONTRACT_TYPE)) {
            $query['tax_query'][] = [
                'taxonomy' => TAX_JOB_OFFER_CONTRACT_TYPE,
                'field' => 'slug',
                'terms' => $term,
            ];
        }

        if ($term = get_query_var(TAX_JOB_OFFER_FIELD)) {
            $query['tax_query'][] = [
                'taxonomy' => TAX_JOB_OFFER_FIELD,
                'field' => 'slug',
                'terms' => $term,
            ];
        }

        if ($job_poster = get_query_var('job-offer-job-poster')) {
            $query['author'] = $job_poster;
        }

        return new \WP_Query($query);
    }
}
