<?php

namespace App\Fields\Partials\Components;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Partial;

class RelatedPublications extends Partial
{
    /**
     * The partial field group.
     */
    public function fields(): Builder
    {
        $fields = Builder::make('related_publications');

        $fields
            ->addText('related_publications_title', [
                'label' => __('Title', 'abbl-backend'),
            ])
            ->addPostObject('related_publications_publications', [
                'label' => __('Publications', 'abbl-backend'),
                'post_type' => CPT_PUBLICATION,
                'multiple' => true,
            ])
            ->addTrueFalse('related_publications_show_link_to_archive', [
                'label' => __('Show link to archive', 'abbl-backend'),
                'default_value' => true,
                'instructions' => __('Show a link to the publications list page.', 'abbl-backend'),
            ]);

        return $fields;
    }
}
