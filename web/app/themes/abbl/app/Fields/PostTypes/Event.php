<?php

namespace App\Fields\PostTypes;

use Log1x\AcfComposer\Builder;
use Log1x\AcfComposer\Field;

class Event extends Field
{
    /**
     * The field group.
     */
    public function fields(): array
    {
        $fields = Builder::make('event');

        $fields
            ->setLocation('post_type', '==', CPT_EVENT);

        $fields
            ->addTrueFalse('is_featured', [
                'label' => __('Featured', 'abbl-backend'),
                'default_value' => false,
            ])
            ->addTab(__('Time and registration', 'abbl-backend'))
                ->addDateTimePicker('start_datetime', [
                    'label' => __('Start', 'abbl-backend'),
                ])
                    ->setWidth(50)
                ->addDateTimePicker('end_datetime', [
                    'label' => __('End', 'abbl-backend'),
                ])
                    ->setWidth(50)
                ->addUrl('registration_url', [
                    'label' => __('Registration URL', 'abbl-backend'),
                ])
            ->addTab(__('Organization', 'abbl-backend'))
                ->addRelationship('organizer', [
                    'label' => __('Organizer', 'abbl-backend'),
                    'post_type' => [CPT_COMPANY],
                    'multiple' => true,
                ])
                ->addRepeater('speakers', [
                    'label' => __('Speakers', 'abbl-backend'),
                ])
                    ->addPostObject('speaker', [
                        'label' => __('Speaker', 'abbl-backend'),
                        'post_type' => CPT_PERSON,
                    ])
                    ->addText('role')
                ->endRepeater();

        return $fields->build();
    }
}
