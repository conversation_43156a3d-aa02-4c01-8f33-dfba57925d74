@props([
    'title' => null,
    'content' => null,
    'showLinkedin' => false,
])

@php
$privacy_policy_url = get_permalink(get_field('page_privacy_policy', 'options'));
@endphp

<x-container>
    <aside class="space-y-4 flex flex-col items-center pt-12 pb-11 px-8 rounded-lg bg-abbl-gradient">
        <h2 class="mb-11 lg:mb-0 font-headings text-[1.75rem] font-bold text-neon-green">
            @if($title)
                {!! $title !!}
            @else
                {!! get_field('newsletter_default_title', 'options') !!}
            @endif
        </h2>
        <form action="#">
            <div class="flex mb-6">
                <input type="email" name="email" placeholder="Your email address" class="w-[416px] px-4 bg-white">
                <button type="submit" class="px-4 py-1 bg-gray-400">{{ __('Subscribe', 'abbl') }}</button>
            </div>
            <label>
                <input type="checkbox" name="optin" id="">
                <span>{!! sprintf(__('I have read and I accept <a href="%s">the ABBL Privacy Policy</a>', 'abbl'), $privacy_policy_url) !!}</span>
            </label>
        </form>
        @if($showLinkedin)
            <div class="flex gap-4">
                <a href="#" class="flex items-center gap-4 font-bold">
                    <i class="fab fa-linkedin text-lg"></i>
                    {{ __('Follow us on LinkedIn for real-time updates', 'abbl') }}
                </a>
            </div>
        @endif
    </aside>
</x-container>
